# Django Settings
SECRET_KEY=your-secret-key-here-change-this-in-production
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/shulexcel_db
# For development, you can use SQLite:
# DATABASE_URL=sqlite:///db.sqlite3

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password-here
DEFAULT_FROM_EMAIL=ShuleXcel <<EMAIL>>

# Frontend URL
FRONTEND_URL=http://localhost:5173

# Redis Configuration (for caching and rate limiting)
REDIS_URL=redis://localhost:6379/0

# Security Settings
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False

# For production, set these to True:
# SECURE_SSL_REDIRECT=True
# SECURE_HSTS_SECONDS=31536000
# SESSION_COOKIE_SECURE=True
# CSRF_COOKIE_SECURE=True

# JWT Settings
JWT_ACCESS_TOKEN_LIFETIME=60
JWT_REFRESH_TOKEN_LIFETIME=1440

# File Upload Settings
MAX_UPLOAD_SIZE=10485760  # 10MB in bytes

# Logging
LOG_LEVEL=INFO

# Sentry (Error Tracking) - Optional
# SENTRY_DSN=your-sentry-dsn-here

# MPESA Configuration (if using)
MPESA_CONSUMER_KEY=your-mpesa-consumer-key
MPESA_CONSUMER_SECRET=your-mpesa-consumer-secret
MPESA_SHORTCODE=your-shortcode
MPESA_PASSKEY=your-passkey
MPESA_CALLBACK_URL=https://your-domain.com/api/mpesa/callback/
