from django.contrib.auth.models import AbstractUser, Group
from django.db import models
from django.conf import settings
# No need for strip_tags as we're creating plain text manually
from django.utils.translation import gettext_lazy as _
from datetime import datetime
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.crypto import get_random_string
from django.apps import apps

# Import the password reset handler
from core.password_reset_handler import password_reset_token_created

class CustomUser(AbstractUser):
    """Custom user model that extends Django's AbstractUser."""
    
    # User type choices
    USER_TYPE_CHOICES = [
        ('system_admin', 'System Administrator'),
        ('school_admin', 'School Administrator'),
        ('deputy_principal', 'Deputy Principal'),
        ('branch_admin', 'Branch Administrator'),
        ('department_head', 'Department Head'),
        ('ict_admin', 'ICT Administrator'),
        ('teacher', 'Teacher'),
        ('student', 'Student'),
        ('parent', 'Parent'),
        ('librarian', 'Librarian'),
        ('counselor', 'Counselor'),
        ('accountant', 'Accountant'),
        ('secretary', 'Secretary'),
        ('nurse', 'Nurse'),
        ('maintenance', 'Maintenance Staff'),
        ('security', 'Security Staff'),
        ('driver', 'Driver'),
    ]

    # Additional fields
    email = models.EmailField(_('email address'), unique=True)
    phone = models.CharField(max_length=15, blank=True, null=True)
    user_type = models.CharField(max_length=20, choices=USER_TYPE_CHOICES, null=True, blank=True)
    profile_picture = models.ImageField(upload_to='profile_pictures/', null=True, blank=True)
    employee_number = models.CharField(max_length=50, blank=True, null=True)
    address = models.CharField(max_length=255, blank=True, null=True)
    date_joined = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    is_first_login = models.BooleanField(default=True)

    # School relationships (optional for superusers)
    school = models.ForeignKey('schools.School', on_delete=models.SET_NULL, null=True, blank=True, related_name='core_users')
    school_branch = models.ForeignKey('schools.SchoolBranch', on_delete=models.SET_NULL, null=True, blank=True, related_name='core_users')
    groups = models.ManyToManyField(
        'auth.Group',
        related_name='core_users',
        blank=True
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        related_name='core_users',
        blank=True
    )
    last_login = models.DateTimeField(null=True, blank=True)
    
    # Profile relationships
    admin_profile = models.OneToOneField('users.AdminProfile', null=True, blank=True, on_delete=models.SET_NULL)
    teacher_profile = models.OneToOneField('users.Teacher', null=True, blank=True, on_delete=models.SET_NULL)
    student_profile = models.OneToOneField('users.Student', null=True, blank=True, on_delete=models.SET_NULL)
    parent_profile = models.OneToOneField('users.Parent', null=True, blank=True, on_delete=models.SET_NULL)
    staff_profile = models.OneToOneField('users.Staff', null=True, blank=True, on_delete=models.SET_NULL)

    # Use email as the username field
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    class Meta:
        verbose_name = _('user')
        verbose_name_plural = _('users')
        ordering = ['-date_joined']

    def __str__(self):
        return self.email

    @property
    def profile(self):
        """Get the active profile based on user_type"""
        profile_type_map = {
            'system_admin': self.admin_profile,
            'school_admin': self.admin_profile,
            'deputy_principal': self.admin_profile,
            'branch_admin': self.admin_profile,
            'department_head': self.admin_profile,
            'ict_admin': self.admin_profile,
            'teacher': self.teacher_profile,
            'student': self.student_profile,
            'parent': self.parent_profile,
            'librarian': self.staff_profile,
            'counselor': self.staff_profile,
            'accountant': self.staff_profile,
            'secretary': self.staff_profile,
            'nurse': self.staff_profile,
            'maintenance': self.staff_profile,
            'security': self.staff_profile,
            'driver': self.staff_profile,
        }
        return profile_type_map.get(self.user_type)

    def get_full_name(self):
        """Return the user's full name."""
        return f"{self.first_name} {self.last_name}".strip()

    def get_short_name(self):
        """Return the user's short name."""
        return self.first_name

    def get_user_type_display(self):
        """Return the display name of the user type."""
        return dict(self.USER_TYPE_CHOICES).get(self.user_type, self.user_type)

    def assign_branch_role(self, role):
        """Assigns user to a branch-specific group"""
        # Safely access profile and school_branch
        profile = self.profile
        if profile and hasattr(profile, 'school_branch') and profile.school_branch:
            group_name = f"{profile.school_branch.code}_{role}"
            try:
                group = Group.objects.get(name=group_name)
            except Group.DoesNotExist:
                # Create the group if it doesn't exist
                group = Group.objects.create(name=group_name)

            self.groups.add(group)
            # No need to call self.save() here, it might cause recursion if called within save

    def assign_default_groups(self):
        """Assigns default groups based on user_type"""
        # Map user types to default groups
        group_mapping = {
            # System Level
            'system_admin': ['System Administrators', 'Administration'],

            # School Administration
            'school_admin': ['School Administrators', 'Administration'],
            'deputy_principal': ['School Administrators', 'Administration'],
            'branch_admin': ['Branch Administrators', 'Administration'],
            'department_head': ['Department Heads', 'Academic Staff'],
            'ict_admin': ['ICT Administrators', 'Support Staff'],

            # Academic Staff
            'teacher': ['Teachers', 'Academic Staff'],
            'librarian': ['Librarians', 'Academic Staff'],
            'counselor': ['Counselors', 'Academic Staff'],

            # Support Staff
            'accountant': ['Accountants', 'Support Staff'],
            'secretary': ['Secretaries', 'Support Staff'],
            'nurse': ['Medical Staff', 'Support Staff'],
            'maintenance': ['Maintenance Staff', 'Support Staff'],
            'security': ['Security Staff', 'Support Staff'],
            'driver': ['Drivers', 'Support Staff'],

            # Students and Parents
            'student': ['Students'],
            'parent': ['Parents']
        }

        # Get the default groups for this user type
        default_groups = group_mapping.get(self.user_type, [])

        # Assign the user to these groups
        for group_name in default_groups:
            try:
                group = Group.objects.get(name=group_name)
            except Group.DoesNotExist:
                # Create the group if it doesn't exist
                group = Group.objects.create(name=group_name)

            self.groups.add(group)

    def save(self, *args, **kwargs):
        if hasattr(self, '_in_save'):  # Prevent recursive saves
            return
        self._in_save = True
        try:
            if not self.username:
                self.username = self.email
            super().save(*args, **kwargs)
            # Call group assignment methods after the user is saved and has a primary key
            # You might need to adjust where these are called depending on your workflow
            # For now, calling them here after initial save:
            # self.assign_default_groups()
            # Example: if self.profile and hasattr(self.profile, 'school_branch'):
            #              self.assign_branch_role('some_role')
        finally:
            self._in_save = False

    # Removed send_password_reset_email as Django's built-in system should be used

    # Removed redundant get_profile and get_all_profiles methods that used UserProfileLink

    # Removed create_profile method as profile creation is handled in the admin form

