from django.shortcuts import get_object_or_404
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status, viewsets, permissions, serializers
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import get_user_model, authenticate
from django.db.models import Q

from .Serializers import LoginSerializer, RegisterSerializer, RegisterStepOneSerializer, RegisterStepTwoSerializer, UserSerializer
from users.models import Teacher
from users.serializers import TeacherSerializer
from communication.models import Notice

# Create a simple NoticeSerializer here to avoid circular imports
class NoticeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notice
        fields = '__all__'

CustomUser = get_user_model()

class UserViewSet(viewsets.ModelViewSet):
    queryset = CustomUser.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['get'])
    def counts(self, request):
        """Get user counts by type, filtered by school and branch if provided"""
        try:
            # Get filter parameters
            school_id = request.query_params.get('school')
            branch_id = request.query_params.get('branch')

            # Start with all users
            queryset = CustomUser.objects.all()

            # Apply filters if provided
            if school_id:
                try:
                    # Filter by school through the school_branch relationship
                    queryset = queryset.filter(
                        Q(admin_profile__school_branch__school_id=school_id) |
                        Q(teacher_profile__school_branch__school_id=school_id) |
                        Q(student_profile__school_branch__school_id=school_id) |
                        Q(staff_profile__school_branch__school_id=school_id)
                    )
                except ValueError:
                    return Response(
                        {"error": "Invalid school ID"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            if branch_id:
                try:
                    # Filter by branch through the profile relationships
                    queryset = queryset.filter(
                        Q(admin_profile__school_branch_id=branch_id) |
                        Q(teacher_profile__school_branch_id=branch_id) |
                        Q(student_profile__school_branch_id=branch_id) |
                        Q(staff_profile__school_branch_id=branch_id)
                    )
                except ValueError:
                    return Response(
                        {"error": "Invalid branch ID"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # Count users by type
            total_students = queryset.filter(user_type='student').count()
            total_teachers = queryset.filter(user_type='teacher').count()
            total_parents = queryset.filter(user_type='parent').count()
            total_staff = queryset.filter(user_type__in=['accountant', 'secretary', 'nurse', 'maintenance', 'security', 'driver', 'librarian', 'counselor']).count()
            total_admins = queryset.filter(user_type__in=['school_admin', 'deputy_principal', 'branch_admin', 'department_head', 'ict_admin', 'system_admin']).count()

            # Return counts
            return Response({
                'totalStudents': total_students,
                'totalTeachers': total_teachers,
                'totalParents': total_parents,
                'totalStaff': total_staff,
                'totalAdmins': total_admins,
                'total': queryset.count()
            })
        except Exception as e:
            return Response(
                {"error": f"Error getting user counts: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AuthViewSet(viewsets.ViewSet):
    permission_classes = [permissions.AllowAny]

    @action(detail=False, methods=['post'])
    def register(self, request):
        # For backward compatibility, use the full RegisterSerializer
        serializer = RegisterSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            refresh = RefreshToken.for_user(user)

            # Include user_type in the response to help frontend redirect to the appropriate form
            response_data = {
                "user": UserSerializer(user).data,
                "user_type": user.user_type,  # Include user_type
                "refresh": str(refresh),
                "access": str(refresh.access_token),
                "profile_id": None  # Will be filled with the profile ID if available
            }

            # Get the profile ID based on user_type
            if user.user_type == 'student':
                if hasattr(user, 'student'):
                    response_data["profile_id"] = user.student.id
            elif user.user_type == 'teacher':
                if hasattr(user, 'teacher'):
                    response_data["profile_id"] = user.teacher.id
            elif user.user_type == 'parent':
                if hasattr(user, 'parent'):
                    response_data["profile_id"] = user.parent.id
            elif user.user_type == 'staff':
                if hasattr(user, 'staff'):
                    response_data["profile_id"] = user.staff.id

            return Response(response_data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def register_step_one(self, request):
        serializer = RegisterStepOneSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            refresh = RefreshToken.for_user(user)

            # Include user_type in the response to help frontend redirect to the appropriate form
            response_data = {
                "user": UserSerializer(user).data,
                "user_type": user.user_type,
                "refresh": str(refresh),
                "access": str(refresh.access_token),
                "profile_id": None
            }

            # Get the profile ID based on user_type
            if user.user_type == 'student':
                if hasattr(user, 'student'):
                    response_data["profile_id"] = user.student.id
            elif user.user_type == 'teacher':
                if hasattr(user, 'teacher'):
                    response_data["profile_id"] = user.teacher.id
            elif user.user_type == 'parent':
                if hasattr(user, 'parent'):
                    response_data["profile_id"] = user.parent.id
            elif user.user_type == 'staff':
                if hasattr(user, 'staff'):
                    response_data["profile_id"] = user.staff.id

            return Response(response_data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['put', 'patch'])
    def register_step_two(self, request, pk=None):
        try:
            user = CustomUser.objects.get(pk=pk)
        except CustomUser.DoesNotExist:
            return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)

        # Check if the authenticated user is the same as the one being updated
        if request.user.is_authenticated and request.user.id != user.id:
            return Response({"error": "You can only update your own profile"}, status=status.HTTP_403_FORBIDDEN)

        serializer = RegisterStepTwoSerializer(user, data=request.data, partial=True)
        if serializer.is_valid():
            updated_user = serializer.save()
            return Response(UserSerializer(updated_user).data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def login(self, request):
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            password = serializer.validated_data["password"]

            # Try to authenticate with email if provided
            if 'email' in serializer.validated_data:
                email = serializer.validated_data["email"]
                user = authenticate(request, email=email, password=password)
            # Otherwise try with username
            elif 'username' in serializer.validated_data:
                username = serializer.validated_data["username"]
                # Try to find a user with this username or email
                try:
                    # First check if the username is actually an email
                    if '@' in username:
                        user = authenticate(request, email=username, password=password)
                    else:
                        # Try to find a user with this username
                        try:
                            user_obj = CustomUser.objects.get(username=username)
                            # Then authenticate with their email
                            user = authenticate(request, email=user_obj.email, password=password)
                        except CustomUser.DoesNotExist:
                            user = None
                except Exception as e:
                    print(f"Authentication error: {e}")
                    user = None
            else:
                user = None

            if user:
                # Check license status for non-superusers
                if not user.is_superuser and hasattr(user, 'school_branch') and user.school_branch:
                    try:
                        from settings_app.license_models import LicenseSubscription
                        license = user.school_branch.school.license
                        if not license.is_active():
                            return Response({
                                'error': 'Your school\'s license has expired or is inactive. Please contact your administrator.'
                            }, status=status.HTTP_403_FORBIDDEN)
                    except (AttributeError, LicenseSubscription.DoesNotExist):
                        # Optional: Block login if no license exists
                        # Uncomment the following lines to enforce license requirement
                        # return Response({
                        #     'error': 'Your school does not have a valid license. Please contact your administrator.'
                        # }, status=status.HTTP_403_FORBIDDEN)
                        pass

                refresh = RefreshToken.for_user(user)

                # Include user_type in the response to help frontend redirect to the appropriate dashboard
                response_data = {
                    "user": UserSerializer(user).data,
                    "user_type": user.user_type,  # Include user_type
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                    "profile_id": None  # Will be filled with the profile ID if available
                }

                # Get the profile ID based on user_type
                if user.user_type == 'student':
                    if hasattr(user, 'student'):
                        response_data["profile_id"] = user.student.id
                elif user.user_type == 'teacher':
                    if hasattr(user, 'teacher'):
                        response_data["profile_id"] = user.teacher.id
                elif user.user_type == 'parent':
                    if hasattr(user, 'parent'):
                        response_data["profile_id"] = user.parent.id
                elif user.user_type == 'staff':
                    if hasattr(user, 'staff'):
                        response_data["profile_id"] = user.staff.id

                return Response(response_data)
            return Response({"error": "Invalid credentials"}, status=status.HTTP_401_UNAUTHORIZED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def logout(self, request):
        try:
            # Simply return success response without blacklisting the token
            # This is a simplified approach since we're not using token blacklist
            return Response({"message": "Successfully logged out."}, status=status.HTTP_205_RESET_CONTENT)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def me(self, request):
        if request.user.is_authenticated:
            serializer = UserSerializer(request.user)
            return Response(serializer.data)
        return Response({"error": "Not authenticated"}, status=status.HTTP_401_UNAUTHORIZED)


class TeacherViewset(viewsets.ModelViewSet):
    queryset = Teacher.objects.all()
    serializer_class = TeacherSerializer
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        queryset = Teacher.objects.all()
        serializer = self.serializer_class(queryset, many=True)
        return Response(serializer.data)

    def retrieve(self, request, pk=None):
        queryset = Teacher.objects.all()
        teacher = get_object_or_404(queryset, pk=pk)
        serializer = self.serializer_class(teacher)
        return Response(serializer.data)


class NoticeViewset(viewsets.ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    queryset = Notice.objects.all()
    serializer_class = NoticeSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            return Response(
                {
                    "message": "Notice created successfully.",
                    "data": serializer.data,
                },
                status=status.HTTP_201_CREATED
            )
        else:
            return Response(
                {
                    "message": "Notice creation failed.",
                    "errors": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST
            )


class DashboardViewSet(viewsets.ViewSet):
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['get'])
    def metrics(self, request):
        # Get the user's school branch
        school_branch = request.user.school_branch

        if not school_branch:
            return Response(
                {"error": "User is not associated with any school branch"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get metrics based on the user's school branch
        try:
            # Count users
            total_users = CustomUser.objects.filter(school_branch=school_branch).count()
            active_users = CustomUser.objects.filter(school_branch=school_branch, is_active=True).count()
            staff_users = CustomUser.objects.filter(school_branch=school_branch, is_staff=True).count()

            # Count teachers
            teachers_count = 0
            try:
                teachers_count = Teacher.objects.filter(school_branch=school_branch).count()
            except:
                pass

            # For other models, we'll just use placeholder values
            # since we don't have access to the actual models
            students_count = 0
            classes_count = 0
            subjects_count = 0

            # Count notices
            notices_count = 0
            try:
                notices_count = Notice.objects.filter(school_branch=school_branch).count()
            except:
                pass

            metrics = {
                "users": {
                    "total": total_users,
                    "active": active_users,
                    "staff": staff_users,
                    "students": students_count,
                    "teachers": teachers_count
                },
                "academics": {
                    "classes": classes_count,
                    "subjects": subjects_count,
                    "notices": notices_count
                }
            }

            return Response(metrics, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
