#!/bin/bash

# Change to the project directory
cd "$(dirname "$0")"

echo "==============================================="
echo "Starting complete data seeding for ShuleXcelApp"
echo "==============================================="

# Step 1: Seed basic data (groups, schools, admin user)
echo -e "\n[1/7] Seeding basic data..."
python manage.py seed_basic_data

# Step 2: Seed academic data (years, terms, grading system)
echo -e "\n[2/7] Seeding academic data..."
python manage.py seed_academic_data

# Step 3: Seed curriculum systems (CBC, 8-4-4, IGCSE)
echo -e "\n[3/7] Seeding curriculum systems..."
python manage.py seed_curriculum_systems

# Step 4: Seed curriculum subjects
echo -e "\n[4/7] Seeding curriculum subjects..."
python manage.py seed_curriculum_subjects

# Step 5: Seed class progression rules
echo -e "\n[5/7] Seeding class progression rules..."
python manage.py seed_class_progression_rules

# Step 6: Seed fleet data
echo -e "\n[6/7] Seeding fleet data..."
python manage.py seed_fleet_data

# Step 7: Seed test data (comprehensive sample data)
echo -e "\n[7/7] Seeding test data (comprehensive)..."
python manage.py seed_test_data

echo -e "\n==============================================="
echo "Data seeding completed successfully!"
echo "==============================================="

echo -e "\nLogin credentials:"
echo "Username: <EMAIL>"
echo "Password: admin123"
echo -e "\n"
