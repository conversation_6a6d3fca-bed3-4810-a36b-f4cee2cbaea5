from django.contrib import admin
# from django.contrib.auth.admin import UserAdmin # Not used in this file
from django.contrib.admin import StackedInline, TabularInline # Standard import for inlines
from .models import (
    AdminProfile,
    Staff,
    Parent,
    Teacher,
    Student,
    ParentStaffLink,
    # Add other models from users app that need admin registration
)
from core.models import CustomUser # Import CustomUser

# Define StackedInlines for each profile model
class AdminProfileInline(StackedInline):
    model = AdminProfile
    fields = ('school_branch', 'admin_number', 'department', 'position', 'date_hired', 'phone_number', 'address', 'date_of_birth', 'gender', 'is_super_admin')
    # Optionally add readonly_fields, list_filter, search_fields if needed for the inline

class StaffInline(StackedInline):
    model = Staff
    fields = ('school_branch', 'staff_number', 'department', 'role', 'phone_number', 'email', 'date_of_birth', 'gender', 'profile_picture')

class ParentInline(StackedInline):
    model = Parent
    fields = ('school_branch', 'phone_number', 'email', 'address', 'gender', 'profile_picture', 'relationship', 'children')

class TeacherInline(StackedInline):
    model = Teacher
    fields = ('school_branch', 'is_class_teacher', 'assigned_classes', 'subjects_taught', 'teacher_number', 'date_of_birth', 'national_id', 'phone_number', 'email', 'personal_email', 'gender', 'profile_picture', 'user_permissions')

class StudentInline(StackedInline):
    model = Student
    fields = ('date_of_birth', 'gender', 'birth_certificate', 'result_slip', 'admission_letter', 'school_branch', 'class_teacher', 'class_name', 'stream', 'admission_number', 'current_class', 'subjects', 'subject_type', 'address', 'phone_number', 'parent', 'emergency_contact', 'additional_notes')
    exclude = ('created_at', 'updated_at')
    
    def save_model(self, request, obj, form, change):
        if not change:  # Only for new objects
            from django.utils import timezone
            obj.created_at = timezone.now()
        super().save_model(request, obj, form, change)

# You can keep or modify existing admin registrations for the profile models if needed
# For example, if you want to access them directly in the admin list view:

# @admin.register(AdminProfile)
# class AdminProfileAdmin(admin.ModelAdmin):
#     list_display = ('user', 'admin_number', 'school_branch')

# @admin.register(Staff)
# class StaffAdmin(admin.ModelAdmin):
#     list_display = ('user', 'staff_number', 'school_branch', 'role')

# @admin.register(Parent)
# class ParentAdmin(admin.ModelAdmin):
#     list_display = ('user', 'phone_number', 'relationship')

# @admin.register(Teacher)
# class TeacherAdmin(admin.ModelAdmin):
#     list_display = ('user', 'teacher_number', 'school_branch', 'is_class_teacher')

# @admin.register(Student)
# class StudentAdmin(admin.ModelAdmin):
#     list_display = ('user', 'admission_number', 'school_branch', 'current_class')

# Register ParentStaffLink if needed
# @admin.register(ParentStaffLink)
# class ParentStaffLinkAdmin(admin.ModelAdmin):
#     list_display = ('staff_user', 'student', 'relationship')

@admin.register(AdminProfile)
class AdminProfileAdmin(admin.ModelAdmin):
    list_display = ('get_full_name', 'get_admin_number', 'get_position', 'get_school')
    list_filter = ('position',)
    search_fields = ('user__email', 'user__first_name', 'user__last_name', 'admin_number')
    
    def get_full_name(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}"
    get_full_name.short_description = 'Name'
    
    def get_admin_number(self, obj):
        return obj.admin_number
    get_admin_number.short_description = 'Admin Number'
    
    def get_position(self, obj):
        return obj.position
    get_position.short_description = 'Position'
    
    def get_school(self, obj):
        return obj.school_branch.name if obj.school_branch else ''
    get_school.short_description = 'School'

@admin.register(Student)
class StudentAdmin(admin.ModelAdmin):
    list_display = ('get_full_name', 'get_admission_number', 'get_class', 'get_stream', 'get_school')
    list_filter = ('class_name', 'stream')
    search_fields = ('user__first_name', 'user__last_name', 'admission_number', 'user__email')
    
    def save_model(self, request, obj, form, change):
        if not change:  # Only for new objects
            from django.utils import timezone
            obj.created_at = timezone.now()
        super().save_model(request, obj, form, change)
    
    def get_full_name(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}"
    get_full_name.short_description = 'Name'
    
    def get_admission_number(self, obj):
        return obj.admission_number
    get_admission_number.short_description = 'Admission Number'
    
    def get_class(self, obj):
        return obj.class_name
    get_class.short_description = 'Class'
    
    def get_stream(self, obj):
        return obj.stream
    get_stream.short_description = 'Stream'
    
    def get_school(self, obj):
        return obj.school_branch.name if obj.school_branch else ''
    get_school.short_description = 'School'

@admin.register(Teacher)
class TeacherAdmin(admin.ModelAdmin):
    list_display = ('get_full_name', 'get_teacher_number', 'get_departments', 'get_school')
    search_fields = ('user__email', 'user__first_name', 'user__last_name', 'teacher_number')
    
    def get_full_name(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}"
    get_full_name.short_description = 'Name'
    
    def get_teacher_number(self, obj):
        return obj.teacher_number
    get_teacher_number.short_description = 'Teacher Number'
    
    def get_departments(self, obj):
        # Assuming 'departments' is a ManyToManyField on the Teacher model or a related manager
        # Adjust access based on your Teacher model definition
        if hasattr(obj, 'department_assignments') and hasattr(obj.department_assignments, 'all'):
            return ", ".join([assignment.department.name for assignment in obj.department_assignments.all()])
        # Fallback if departments is a ForeignKey (assuming 'department' field exists)
        elif hasattr(obj, 'department') and obj.department:
            return obj.department.name
        return ''
    get_departments.short_description = 'Departments'
    
    def get_school(self, obj):
        return obj.school_branch.name if obj.school_branch else ''
    get_school.short_description = 'School'

@admin.register(Parent)
class ParentAdmin(admin.ModelAdmin):
    list_display = ('get_full_name', 'get_phone_number', 'get_relationship', 'get_school')
    search_fields = ('user__email', 'user__first_name', 'user__last_name', 'phone_number')
    
    def get_full_name(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}"
    get_full_name.short_description = 'Name'
    
    def get_phone_number(self, obj):
        return obj.phone_number
    get_phone_number.short_description = 'Phone'
    
    def get_relationship(self, obj):
        return obj.relationship
    get_relationship.short_description = 'Relationship'
    
    def get_school(self, obj):
        # Assuming school_branch is a ManyToManyField on the Parent model
        if hasattr(obj, 'school_branch') and hasattr(obj.school_branch, 'all'):
            return ", ".join([branch.name for branch in obj.school_branch.all()])
        # Fallback if school_branch is a ForeignKey or OneToOneField
        elif hasattr(obj, 'school_branch') and obj.school_branch:
            return obj.school_branch.name
        return ''
    get_school.short_description = 'School'

@admin.register(Staff)
class StaffAdmin(admin.ModelAdmin):
    list_display = ('get_full_name', 'get_staff_number', 'get_role', 'get_school')
    list_filter = ('role', 'department')
    search_fields = ('user__email', 'user__first_name', 'user__last_name', 'staff_number')
    
    def get_full_name(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}"
    get_full_name.short_description = 'Name'
    
    def get_staff_number(self, obj):
        return obj.staff_number
    get_staff_number.short_description = 'Staff Number'
    
    def get_role(self, obj):
        return obj.role
    get_role.short_description = 'Role'
    
    def get_school(self, obj):
        return obj.school_branch.name if obj.school_branch else ''
    get_school.short_description = 'School'
