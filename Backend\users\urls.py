from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    StaffViewSet, ParentViewSet, TeacherViewSet, StudentViewSet, AdminProfileViewSet,
    LogoutView, CustomTokenVerifyView, CustomTokenRefreshView,
    UserProfileView, ProfileImageView, LoginView, UserCreateView, ParentStaffLinkViewSet, UserProfileViewSet
)

from .role_views import (
    SchoolAdminViewSet, DeputyPrincipalViewSet, DepartmentHeadViewSet, ICTAdminViewSet,
    LibrarianViewSet, CounselorViewSet, AccountantViewSet, StaffChildrenViewSet
)

router = DefaultRouter()
router.register('students', StudentViewSet, basename='student')
router.register('teachers', TeacherViewSet, basename='teacher')
router.register('parents', ParentViewSet, basename='parent')
router.register('staff', StaffViewSet, basename='staff')
router.register('admins', AdminProfileViewSet, basename='admin')
router.register('parent-staff-links', ParentStaffLinkViewSet, basename='parent-staff-link')
router.register('profiles', UserProfileViewSet, basename='profile')

# Role-specific endpoints
router.register('school-admins', SchoolAdminViewSet, basename='school-admin')
router.register('deputy-principals', DeputyPrincipalViewSet, basename='deputy-principal')
router.register('department-heads', DepartmentHeadViewSet, basename='department-head')
router.register('ict-admins', ICTAdminViewSet, basename='ict-admin')
router.register('librarians', LibrarianViewSet, basename='librarian')
router.register('counselors', CounselorViewSet, basename='counselor')
router.register('accountants', AccountantViewSet, basename='accountant')
router.register('staff-children', StaffChildrenViewSet, basename='staff-children')

urlpatterns = [
    path('', include(router.urls)),

    # Authentication
    path('login/', LoginView.as_view(), name='login'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('token/refresh/', CustomTokenRefreshView.as_view(), name='token_refresh'),
    path('token/verify/', CustomTokenVerifyView.as_view(), name='token_verify'),

    # User Profile
    path('profile/', UserProfileView.as_view(), name='user-profile'),
    path('profile/complete-first-login/', UserProfileView.as_view(), name='complete-first-login'),
    path('profile/complete-profile/', UserProfileView.as_view(), name='complete-current-profile'),
    path('change-password/', UserProfileView.as_view(), name='change-password'),
    path('me/profile-image/', ProfileImageView.as_view(), name='profile-image'),

    # User Management
    path('create/', UserCreateView.as_view(), name='user-create'),
]
