from rest_framework import viewsets, status, filters
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from core.permissions import RoleBasedPermissionMixin
import logging

# Set up logger
logger = logging.getLogger(__name__)
from .models import Staff, Parent, Teacher, Student, AdminProfile, ParentStaffLink
from .serializers import (
    StaffSerializer, ParentSerializer, TeacherSerializer,
    StudentSerializer, UserProfileSerializer, UserCreateSerializer,
    AdminProfileSerializer, ParentStaffLinkSerializer
)
from rest_framework_simplejwt.exceptions import TokenError
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenVerifyView, TokenRefreshView
from rest_framework.parsers import MultiPartParser, FormParser
from django.contrib.auth import get_user_model, authenticate
from django.db.models import Q
from rest_framework.pagination import PageNumberPagination
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

User = get_user_model()

class BaseViewSet(RoleBasedPermissionMixin, viewsets.ModelViewSet):
    """
    Base ViewSet to handle school branch filtering dynamically.
    """
    permission_classes = [IsAuthenticated]
    required_groups = ['Administration', 'School Administrators', 'Academic Staff', 'Teachers']
    def get_queryset(self):
        queryset = super().get_queryset()
        school_branch = self.request.query_params.get('school_branch')
        if school_branch:
            queryset = queryset.filter(school_branch_id=school_branch)
        return queryset

class StaffViewSet(BaseViewSet):
    queryset = Staff.objects.all()
    serializer_class = StaffSerializer
    required_groups = ['Administration', 'School Administrators']

class ParentViewSet(BaseViewSet):
    queryset = Parent.objects.all()
    serializer_class = ParentSerializer
    required_groups = ['Administration', 'School Administrators', 'Academic Staff', 'Teachers']

class TeacherViewSet(BaseViewSet):
    queryset = Teacher.objects.all()
    serializer_class = TeacherSerializer
    required_groups = ['Administration', 'School Administrators', 'Academic Staff']

class AdminProfileViewSet(BaseViewSet):
    queryset = AdminProfile.objects.all()
    serializer_class = AdminProfileSerializer
    required_groups = ['Administration', 'School Administrators']

class UserProfileViewSet(BaseViewSet):
    queryset = User.objects.all()
    serializer_class = UserProfileSerializer
    required_groups = ['Administration', 'School Administrators', 'Academic Staff', 'Teachers', 'Students', 'Parents']


class ParentStaffLinkViewSet(BaseViewSet):
    """
    ViewSet for managing links between staff members and their children who are students.
    This allows staff members to access their children's information without needing a separate parent account.
    """
    queryset = ParentStaffLink.objects.all()
    serializer_class = ParentStaffLinkSerializer
    required_groups = ['Administration', 'School Administrators', 'Academic Staff', 'Teachers']

    def get_queryset(self):
        """
        Filter queryset based on the current user:
        - System admins and school admins can see all links
        - Staff members can only see their own links
        """
        queryset = super().get_queryset()
        user = self.request.user

        # If the user is a superuser or has admin privileges, return all links
        if user.is_superuser or user.user_type in ['system_admin', 'school_admin', 'branch_admin']:
            return queryset

        # Otherwise, return only links where the current user is the staff member
        return queryset.filter(staff_user=user)

class StandardResultsSetPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

class StudentViewSet(BaseViewSet):
    queryset = Student.objects.all()
    serializer_class = StudentSerializer
    pagination_class = StandardResultsSetPagination
    required_groups = ['Administration', 'School Administrators', 'Academic Staff', 'Teachers', 'Finance']
    filter_backends = [filters.SearchFilter]
    search_fields = ['first_name', 'last_name', 'admission_number']

    def get_queryset(self):
        """
        Returns filtered queryset based on request parameters.
        Filters by grade, branch and searches by name or admission number.
        """
        try:
            queryset = super().get_queryset()

            # Apply school branch filter first
            school_branch = self.request.query_params.get('school_branch')
            if school_branch:
                queryset = queryset.filter(school_branch_id=school_branch)

            # Get other filter parameters from request
            grade = self.request.query_params.get('grade')
            class_id = self.request.query_params.get('class_name')  # Changed from 'class' to 'class_name'
            stream_id = self.request.query_params.get('stream')
            search_term = self.request.query_params.get('search', '').strip()

            print(f"Filtering students with class_name={class_id}, stream={stream_id}")

            # Apply grade filter if specified and not 'all'
            if grade and grade.lower() != 'all':
                queryset = queryset.filter(grade=grade)

            # Apply class filter if specified
            if class_id:
                queryset = queryset.filter(class_name_id=class_id)

            # Apply stream filter if specified
            if stream_id:
                queryset = queryset.filter(stream_id=stream_id)

            # Apply search filter if search term provided
            if search_term:
                queryset = queryset.filter(
                    Q(first_name__icontains=search_term) |
                    Q(last_name__icontains=search_term) |
                    Q(admission_number__icontains=search_term)
                )

            return queryset.order_by('admission_number')

        except Exception as e:
            logger.error(f"Error filtering students: {str(e)}", exc_info=True)
            return Student.objects.none()

class LogoutView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        refresh_token = request.data.get('refresh_token') or request.COOKIES.get('refresh_token')
        if not refresh_token:
            return Response({'error': 'Refresh token required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            token = RefreshToken(refresh_token)
            token.blacklist()
            return Response({"message": "Successfully logged out."}, status=status.HTTP_200_OK)
        except (TokenError, AttributeError):
            return Response({"error": "Invalid or expired token"}, status=status.HTTP_400_BAD_REQUEST)

class CustomTokenVerifyView(TokenVerifyView):
    def post(self, request, *args, **kwargs):
        try:
            return super().post(request, *args, **kwargs)
        except TokenError as e:
            return Response({'error': str(e)}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception:
            return Response({'error': 'Token verification failed'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class CustomTokenRefreshView(TokenRefreshView):
    def post(self, request, *args, **kwargs):
        try:
            return super().post(request, *args, **kwargs)
        except TokenError as e:
            return Response({'error': str(e)}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception:
            return Response({'error': 'Token refresh failed'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class UserProfileView(RoleBasedPermissionMixin, APIView):
    permission_classes = [IsAuthenticated]
    required_groups = ['Administration', 'School Administrators', 'Academic Staff', 'Teachers', 'Students', 'Parents']

    def get(self, request):
        """Get the current user's profile."""
        try:
            # Log the user attempting to access their profile
            logger.info(f"User {request.user.email} attempting to access profile")
            
            # Check if user has required attributes
            if not hasattr(request.user, 'user_type'):
                logger.error(f"User {request.user.email} missing user_type attribute")
                return Response(
                    {"error": "User profile is incomplete. Please contact support."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get the profile data
            serializer = UserProfileSerializer(request.user)
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error fetching profile for user {request.user.email}: {str(e)}", exc_info=True)
            return Response(
                {"error": "An error occurred while fetching your profile. Please try again later."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def patch(self, request):
        """Update the current user's profile."""
        try:
            serializer = UserProfileSerializer(request.user, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error updating profile for user {request.user.email}: {str(e)}", exc_info=True)
            return Response(
                {"error": "An error occurred while updating your profile. Please try again later."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def post(self, request):
        """Complete current user's profile."""
        try:
            user = request.user

            # Update basic user information
            if 'first_name' in request.data:
                user.first_name = request.data.get('first_name')
            if 'last_name' in request.data:
                user.last_name = request.data.get('last_name')
            if 'phone' in request.data:
                user.phone = request.data.get('phone')
            if 'address' in request.data:
                user.address = request.data.get('address')

            # Handle profile picture upload
            if 'profile_picture' in request.FILES:
                user.profile_picture = request.FILES['profile_picture']

            # Mark first login as complete
            user.is_first_login = False
            user.save()

            # Try to create or update user-specific profile based on user_type
            user_type = getattr(user, 'user_type', None)
            if user_type:
                try:
                    self._create_or_update_user_profile(user, request.data, user_type)
                except Exception as profile_error:
                    logger.warning(f"Could not create/update profile for user {user.email}: {profile_error}")
                    # Don't fail the entire request if profile creation fails

            # Return updated user data
            serializer = UserProfileSerializer(user)

            logger.info(f"User {user.email} completed profile")

            return Response({
                "message": "Profile completed successfully",
                "user": serializer.data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error completing profile: {str(e)}", exc_info=True)
            return Response(
                {"error": "Failed to complete profile"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )



    def _create_or_update_user_profile(self, user, data, user_type):
        """Helper method to create or update user-specific profiles"""
        from users.models import Student, Teacher, Parent, Staff, AdminProfile

        profile_data = {
            'date_of_birth': data.get('date_of_birth'),
            'emergency_contact': data.get('emergency_contact'),
            'emergency_phone': data.get('emergency_phone'),
        }

        # Remove None values
        profile_data = {k: v for k, v in profile_data.items() if v is not None}

        if user_type == 'student':
            profile, created = Student.objects.get_or_create(user=user)
            for key, value in profile_data.items():
                if hasattr(profile, key):
                    setattr(profile, key, value)
            profile.save()
        elif user_type == 'teacher':
            profile, created = Teacher.objects.get_or_create(user=user)
            for key, value in profile_data.items():
                if hasattr(profile, key):
                    setattr(profile, key, value)
            profile.save()
        elif user_type == 'parent':
            profile, created = Parent.objects.get_or_create(user=user)
            for key, value in profile_data.items():
                if hasattr(profile, key):
                    setattr(profile, key, value)
            profile.save()
        elif user_type in ['staff', 'librarian', 'counselor', 'accountant', 'secretary', 'nurse', 'maintenance', 'security', 'driver']:
            profile, created = Staff.objects.get_or_create(user=user)
            for key, value in profile_data.items():
                if hasattr(profile, key):
                    setattr(profile, key, value)
            profile.save()
        elif user_type in ['system_admin', 'school_admin', 'deputy_principal', 'branch_admin', 'department_head', 'ict_admin']:
            profile, created = AdminProfile.objects.get_or_create(user=user)
            for key, value in profile_data.items():
                if hasattr(profile, key):
                    setattr(profile, key, value)
            profile.save()



    def put(self, request):
        """Handle first login completion and password changes"""
        # Check if this is a first login completion request
        if request.data.get('action') == 'complete_first_login':
            try:
                user = request.user
                user.is_first_login = False
                user.save()
                logger.info(f"User {user.email} completed first login")
                return Response({"message": "First login completed successfully"}, status=status.HTTP_200_OK)
            except Exception as e:
                logger.error(f"Error completing first login: {str(e)}", exc_info=True)
                return Response({"error": "Failed to complete first login"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Handle password change
        return self._change_password(request)

    def _change_password(self, request):
        """Change user password"""
        try:
            user = request.user
            current_password = request.data.get('current_password')
            new_password = request.data.get('new_password')
            confirm_password = request.data.get('confirm_password')

            # Validate input
            if not current_password or not new_password or not confirm_password:
                return Response(
                    {"detail": "All password fields are required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if new_password != confirm_password:
                return Response(
                    {"detail": "New password and confirmation do not match"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check current password
            if not user.check_password(current_password):
                return Response(
                    {"detail": "Current password is incorrect"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate password strength
            if len(new_password) < 8:
                return Response(
                    {"detail": "Password must be at least 8 characters long"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Set new password
            user.set_password(new_password)
            user.save()

            # Log the action
            logger.info(f"User {user.email} changed their password")

            return Response({"message": "Password changed successfully"}, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error changing password: {str(e)}", exc_info=True)
            return Response({"error": "Failed to change password"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class ProfileImageView(RoleBasedPermissionMixin, APIView):
    parser_classes = (MultiPartParser, FormParser)
    permission_classes = [IsAuthenticated]
    required_groups = ['Administration', 'School Administrators', 'Academic Staff', 'Teachers', 'Students', 'Parents']

    def post(self, request):
        profile_picture = request.FILES.get('profile_picture')
        if not profile_picture:
            return Response({'error': 'No image provided'}, status=status.HTTP_400_BAD_REQUEST)

        request.user.profile_picture = profile_picture
        request.user.save()

        # Get the full URL including domain
        profile_picture_url = request.build_absolute_uri(request.user.profile_picture.url)

        return Response({
            'profile_picture_url': profile_picture_url,
            'message': 'Profile picture updated successfully'
        }, status=status.HTTP_200_OK)

class LoginView(APIView):
    def post(self, request):
        email = request.data.get('email')
        password = request.data.get('password')

        if not email or not password:
            return Response({'error': 'Email and password are required'}, status=status.HTTP_400_BAD_REQUEST)

        user = authenticate(username=email, password=password)  # Ensure correct AUTH_USER_MODEL usage

        if user is None:
            return Response({'error': 'Invalid credentials'}, status=status.HTTP_401_UNAUTHORIZED)

        # Check license status for non-superusers
        if not user.is_superuser and hasattr(user, 'school_branch') and user.school_branch:
            try:
                from settings_app.license_models import LicenseSubscription
                license = user.school_branch.school.license
                if not license.is_active():
                    return Response({
                        'error': 'Your school\'s license has expired or is inactive. Please contact your administrator.'
                    }, status=status.HTTP_403_FORBIDDEN)
            except (AttributeError, LicenseSubscription.DoesNotExist):
                # Optional: Block login if no license exists
                # Uncomment the following lines to enforce license requirement
                # return Response({
                #     'error': 'Your school does not have a valid license. Please contact your administrator.'
                # }, status=status.HTTP_403_FORBIDDEN)
                pass

        refresh = RefreshToken.for_user(user)

        # Get profile data based on user type
        profile_data = None

        # Use the UserProfileSerializer to get the appropriate profile
        user_serializer = UserProfileSerializer(user)
        if 'profile' in user_serializer.data:
            profile_data = user_serializer.data['profile']

        response_data = {
            'access': str(refresh.access_token),
            'refresh': str(refresh),
            'user': {
                'id': user.id,
                'email': user.email,
                'user_type': getattr(user, 'user_type', 'N/A'),
                'profile': profile_data
            }
        }

        response = Response(response_data, status=status.HTTP_200_OK)
        response.set_cookie(
            'refresh_token', str(refresh),
            httponly=True, secure=True, samesite='Strict', max_age=3600 * 24 * 14
        )

        return response


class UserCreateView(RoleBasedPermissionMixin, APIView):
    """Enhanced API view for creating users with any role."""
    permission_classes = [IsAuthenticated]
    required_groups = ['Administration', 'School Administrators']
    parser_classes = (MultiPartParser, FormParser)

    def post(self, request):
        """Create a new user with the specified role or update an existing one."""
        try:
            # Default update_if_exists to False if not provided
            data = request.data.copy()
            if 'update_if_exists' not in data:
                data['update_if_exists'] = False

            # Set default send_welcome_email to True if not provided
            if 'send_welcome_email' not in data:
                data['send_welcome_email'] = True

            # Log the request data for debugging (excluding sensitive fields)
            debug_data = data.copy()
            if 'password' in debug_data:
                debug_data['password'] = '********'
            logger.debug(f"User creation request: {debug_data}")

            # Validate and save the user
            serializer = UserCreateSerializer(data=data)
            if serializer.is_valid():
                user_data = serializer.save()

                # Determine the appropriate status code based on whether we created or updated
                status_code = status.HTTP_201_CREATED
                if user_data.get('action_performed') == 'updated':
                    status_code = status.HTTP_200_OK

                # Add a success message
                action = "updated" if user_data.get('action_performed') == 'updated' else "created"
                user_data['message'] = f"User successfully {action}."

                # Log the success
                logger.info(f"User {action}: {user_data['email']} ({user_data['user_type']})")

                return Response(user_data, status=status_code)
            else:
                # Log validation errors
                logger.warning(f"User creation validation errors: {serializer.errors}")
                return Response({
                    'errors': serializer.errors,
                    'message': 'Validation failed. Please check the form for errors.'
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            # Log unexpected errors
            logger.error(f"Unexpected error in user creation: {str(e)}", exc_info=True)
            return Response({
                'error': 'An unexpected error occurred while processing your request.',
                'detail': str(e) if settings.DEBUG else 'Please try again later.'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get(self, request):
        """Get user creation form metadata."""
        try:
            # Return metadata about the user creation form
            metadata = {
                'user_types': dict(User.USER_TYPE_CHOICES),
                'gender_choices': [{'value': 'M', 'label': 'Male'}, {'value': 'F', 'label': 'Female'}],
                'relationship_choices': [
                    {'value': 'Father', 'label': 'Father'},
                    {'value': 'Mother', 'label': 'Mother'},
                    {'value': 'Guardian', 'label': 'Guardian'},
                    {'value': 'Other', 'label': 'Other'}
                ],
                'max_file_sizes': {
                    'profile_picture': '2MB',
                    'documents': '5MB'
                }
            }
            return Response(metadata, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error fetching user creation metadata: {str(e)}", exc_info=True)
            return Response({'error': 'Failed to fetch form metadata'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
