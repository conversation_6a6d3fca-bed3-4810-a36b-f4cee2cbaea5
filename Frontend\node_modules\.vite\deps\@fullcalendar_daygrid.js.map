{"version": 3, "sources": ["../../@fullcalendar/daygrid/index.js"], "sourcesContent": ["import { createPlugin } from '@fullcalendar/core/index.js';\nimport { DayGridView as DayTableView, TableDateProfileGenerator } from './internal.js';\nimport '@fullcalendar/core/internal.js';\nimport '@fullcalendar/core/preact.js';\n\nvar index = createPlugin({\n    name: '@fullcalendar/daygrid',\n    initialView: 'dayGridMonth',\n    views: {\n        dayGrid: {\n            component: DayTableView,\n            dateProfileGeneratorClass: TableDateProfileGenerator,\n        },\n        dayGridDay: {\n            type: 'dayGrid',\n            duration: { days: 1 },\n        },\n        dayGridWeek: {\n            type: 'dayGrid',\n            duration: { weeks: 1 },\n        },\n        dayGridMonth: {\n            type: 'dayGrid',\n            duration: { months: 1 },\n            fixedWeekCount: true,\n        },\n        dayGridYear: {\n            type: 'dayGrid',\n            duration: { years: 1 },\n        },\n    },\n});\n\nexport { index as default };\n"], "mappings": ";;;;;;;;;;AAKA,IAAI,QAAQ,aAAa;AAAA,EACrB,MAAM;AAAA,EACN,aAAa;AAAA,EACb,OAAO;AAAA,IACH,SAAS;AAAA,MACL,WAAW;AAAA,MACX,2BAA2B;AAAA,IAC/B;AAAA,IACA,YAAY;AAAA,MACR,MAAM;AAAA,MACN,UAAU,EAAE,MAAM,EAAE;AAAA,IACxB;AAAA,IACA,aAAa;AAAA,MACT,MAAM;AAAA,MACN,UAAU,EAAE,OAAO,EAAE;AAAA,IACzB;AAAA,IACA,cAAc;AAAA,MACV,MAAM;AAAA,MACN,UAAU,EAAE,QAAQ,EAAE;AAAA,MACtB,gBAAgB;AAAA,IACpB;AAAA,IACA,aAAa;AAAA,MACT,MAAM;AAAA,MACN,UAAU,EAAE,OAAO,EAAE;AAAA,IACzB;AAAA,EACJ;AACJ,CAAC;", "names": []}