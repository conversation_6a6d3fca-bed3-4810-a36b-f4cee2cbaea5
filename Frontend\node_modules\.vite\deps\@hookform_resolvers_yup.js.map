{"version": 3, "sources": ["../../@hookform/resolvers/src/validateFieldsNatively.ts", "../../@hookform/resolvers/src/toNestErrors.ts", "../../@hookform/resolvers/yup/src/yup.ts"], "sourcesContent": ["import {\n  get,\n  FieldError,\n  ResolverOptions,\n  Ref,\n  FieldErrors,\n  FieldValues,\n} from 'react-hook-form';\n\nconst setCustomValidity = (\n  ref: Ref,\n  fieldPath: string,\n  errors: FieldErrors,\n) => {\n  if (ref && 'reportValidity' in ref) {\n    const error = get(errors, fieldPath) as FieldError | undefined;\n    ref.setCustomValidity((error && error.message) || '');\n\n    ref.reportValidity();\n  }\n};\n\n// Native validation (web only)\nexport const validateFieldsNatively = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): void => {\n  for (const fieldPath in options.fields) {\n    const field = options.fields[fieldPath];\n    if (field && field.ref && 'reportValidity' in field.ref) {\n      setCustomValidity(field.ref, fieldPath, errors);\n    } else if (field.refs) {\n      field.refs.forEach((ref: HTMLInputElement) =>\n        setCustomValidity(ref, fieldPath, errors),\n      );\n    }\n  }\n};\n", "import {\n  set,\n  get,\n  FieldErrors,\n  Field,\n  ResolverOptions,\n  FieldValues,\n  InternalFieldName,\n} from 'react-hook-form';\nimport { validateFieldsNatively } from './validateFieldsNatively';\n\nexport const toNestErrors = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): FieldErrors<TFieldValues> => {\n  options.shouldUseNativeValidation && validateFieldsNatively(errors, options);\n\n  const fieldErrors = {} as FieldErrors<TFieldValues>;\n  for (const path in errors) {\n    const field = get(options.fields, path) as Field['_f'] | undefined;\n    const error = Object.assign(errors[path] || {}, {\n      ref: field && field.ref,\n    });\n\n    if (isNameInFieldArray(options.names || Object.keys(errors), path)) {\n      const fieldArrayErrors = Object.assign({}, get(fieldErrors, path));\n\n      set(fieldArrayErrors, 'root', error);\n      set(fieldErrors, path, fieldArrayErrors);\n    } else {\n      set(fieldErrors, path, error);\n    }\n  }\n\n  return fieldErrors;\n};\n\nconst isNameInFieldArray = (\n  names: InternalFieldName[],\n  name: InternalFieldName,\n) => names.some((n) => n.startsWith(name + '.'));\n", "import * as Yup from 'yup';\nimport { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport {\n  appendErrors,\n  FieldError,\n  FieldValues,\n  Resolver,\n} from 'react-hook-form';\n\n/**\n * Why `path!` ? because it could be `undefined` in some case\n * https://github.com/jquense/yup#validationerrorerrors-string--arraystring-value-any-path-string\n */\nconst parseErrorSchema = (\n  error: Yup.ValidationError,\n  validateAllFieldCriteria: boolean,\n) => {\n  return (error.inner || []).reduce<Record<string, FieldError>>(\n    (previous, error) => {\n      if (!previous[error.path!]) {\n        previous[error.path!] = { message: error.message, type: error.type! };\n      }\n\n      if (validateAllFieldCriteria) {\n        const types = previous[error.path!].types;\n        const messages = types && types[error.type!];\n\n        previous[error.path!] = appendErrors(\n          error.path!,\n          validateAllFieldCriteria,\n          previous,\n          error.type!,\n          messages\n            ? ([] as string[]).concat(messages as string[], error.message)\n            : error.message,\n        ) as FieldError;\n      }\n\n      return previous;\n    },\n    {},\n  );\n};\n\nexport function yupResolver<TFieldValues extends FieldValues>(\n  schema:\n    | Yup.ObjectSchema<TFieldValues>\n    | ReturnType<typeof Yup.lazy<Yup.ObjectSchema<TFieldValues>>>,\n  schemaOptions: Parameters<(typeof schema)['validate']>[1] = {},\n  resolverOptions: {\n    /**\n     * @default async\n     */\n    mode?: 'async' | 'sync';\n    /**\n     * Return the raw input values rather than the parsed values.\n     * @default false\n     */\n    raw?: boolean;\n  } = {},\n): Resolver<Yup.InferType<typeof schema>> {\n  return async (values, context, options) => {\n    try {\n      if (schemaOptions.context && process.env.NODE_ENV === 'development') {\n        // eslint-disable-next-line no-console\n        console.warn(\n          \"You should not used the yup options context. Please, use the 'useForm' context object instead\",\n        );\n      }\n\n      const result = await schema[\n        resolverOptions.mode === 'sync' ? 'validateSync' : 'validate'\n      ](\n        values,\n        Object.assign({ abortEarly: false }, schemaOptions, { context }),\n      );\n\n      options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n      return {\n        values: resolverOptions.raw ? values : result,\n        errors: {},\n      };\n    } catch (e: any) {\n      if (!e.inner) {\n        throw e;\n      }\n\n      return {\n        values: {},\n        errors: toNestErrors(\n          parseErrorSchema(\n            e,\n            !options.shouldUseNativeValidation &&\n              options.criteriaMode === 'all',\n          ),\n          options,\n        ),\n      };\n    }\n  };\n}\n"], "mappings": ";;;;;;;;;AASA,IAAMA,IAAoB,SACxBC,GACAC,IACAC,IAAAA;AAEA,MAAIF,KAAO,oBAAoBA,GAAK;AAClC,QAAMG,KAAQC,IAAIF,IAAQD,EAAAA;AAC1BD,MAAID,kBAAmBI,MAASA,GAAME,WAAY,EAAA,GAElDL,EAAIM,eAAAA;EACL;AACH;AAXA,IAcaC,IAAyB,SACpCL,GACAM,GAAAA;AACQ,MAAAC,KAAAA,SAAAR,IAAAA;AAEN,QAAMS,KAAQF,EAAQG,OAAOV,EAAAA;AACzBS,IAAAA,MAASA,GAAMV,OAAO,oBAAoBU,GAAMV,MAClDD,EAAkBW,GAAMV,KAAKC,IAAWC,CAAAA,IAC/BQ,GAAME,QACfF,GAAME,KAAKC,QAAQ,SAACb,IAAAA;AAAqB,aACvCD,EAAkBC,IAAKC,IAAWC,CAAAA;IAAO,CAAA;EAG9C;AATD,WAAWD,MAAaO,EAAQG,OAAMF,CAAAA,GAAAR,EAAAA;AAUxC;AA5BA,ICEaa,IAAe,SAC1BZ,IACAM,IAAAA;AAEAA,EAAAA,GAAQO,6BAA6BR,EAAuBL,IAAQM,EAAAA;AAEpE,MAAMQ,IAAc,CAAA;AACpB,WAAWC,KAAQf,IAAQ;AACzB,QAAMQ,IAAQN,IAAII,GAAQG,QAAQM,CAAAA,GAC5Bd,IAAQe,OAAOC,OAAOjB,GAAOe,CAAAA,KAAS,CAAE,GAAE,EAC9CjB,KAAKU,KAASA,EAAMV,IAAAA,CAAAA;AAGtB,QAAIoB,EAAmBZ,GAAQa,SAASH,OAAOI,KAAKpB,EAAAA,GAASe,CAAAA,GAAO;AAClE,UAAMM,IAAmBL,OAAOC,OAAO,CAAA,GAAIf,IAAIY,GAAaC,CAAAA,CAAAA;AAE5DO,UAAID,GAAkB,QAAQpB,CAAAA,GAC9BqB,IAAIR,GAAaC,GAAMM,CAAAA;IACxB,MACCC,KAAIR,GAAaC,GAAMd,CAAAA;EAE1B;AAED,SAAOa;AACT;AD1BA,IC4BMI,IAAqB,SACzBC,GACAI,GAAAA;AAAuB,SACpBJ,EAAMK,KAAK,SAACC,IAAAA;AAAM,WAAAA,GAAEC,WAAWH,IAAO,GAAA;EAAI,CAAA;AAAC;;;ACIhC,SAAAI,GACdC,IAGAC,IACAC,GAAAA;AAYA,SAAA,WAbAD,OAAAA,KAA4D,CAAE,IAAA,WAC9DC,MAAAA,IAUI,CAAA,IAEUC,SAAAA,GAAQC,IAASC,GAAAA;AAAW,QAAA;AAAA,aAAAC,QAAAC,QAAAA,SAAAA,IAAAA,GAAAA;AAAAA,YAAAA;AAAAA,cAAAA,KAElCN,GAAcG,WAAoC,QAEpDI,QAAQC,KACN,+FAAA,GAEHH,QAAAC,QAEoBP,GACM,WAAzBE,EAAgBQ,OAAkB,iBAAiB,UAAA,EAEnDP,GACAQ,OAAOC,OAAO,EAAEC,YAAAA,MAAY,GAASZ,IAAe,EAAEG,SAAAA,GAAAA,CAAAA,CAAAA,CAAAA,EACvDU,KAAAA,SALKC,IAAAA;AASN,mBAFAV,EAAQW,6BAA6BC,EAAuB,CAAE,GAAEZ,CAAAA,GAEzD,EACLF,QAAQD,EAAgBgB,MAAMf,IAASY,IACvCI,QAAQ,CAAA,EAAA;UACR,CAAA;QAAA,SAAA,GAAA;AAAA,iBAAA,EAAA,CAAA;QAAA;AAAA,eAAA,KAAA,EAAA,OAAA,EAAA,KAAA,QAAA,CAAA,IAAA;MAAA,EArBoC,GAAA,SAsB/BC,GAAAA;AACP,YAAA,CAAKA,EAAEC,MACL,OAAMD;AAGR,eAAO,EACLjB,QAAQ,CAAE,GACVgB,QAAQG,GA5EdC,KA8EUH,GA7EVI,KAAAA,CA8EWnB,EAAQW,6BACkB,UAAzBX,EAAQoB,eA7EZF,GAAMF,SAAS,CAAA,GAAIK,OACzB,SAACC,IAAUJ,IAAAA;AAKT,cAJKI,GAASJ,GAAMK,IAAAA,MAClBD,GAASJ,GAAMK,IAAAA,IAAS,EAAEC,SAASN,GAAMM,SAASC,MAAMP,GAAMO,KAAAA,IAG5DN,IAA0B;AAC5B,gBAAMO,KAAQJ,GAASJ,GAAMK,IAAAA,EAAOG,OAC9BC,KAAWD,MAASA,GAAMR,GAAMO,IAAAA;AAEtCH,YAAAA,GAASJ,GAAMK,IAAAA,IAASK,aACtBV,GAAMK,MACNJ,IACAG,IACAJ,GAAMO,MACNE,KACK,CAAA,EAAgBE,OAAOF,IAAsBT,GAAMM,OAAAA,IACpDN,GAAMM,OAAAA;UAEb;AAED,iBAAOF;QACT,GACA,CAAA,CAAA,IAwDMtB,CAAAA,EAAAA;AAnFe,YACvBkB,IACAC;MAoFG,CAAA,CAAA;IACH,SAACJ,GAAAA;AAAA,aAAAd,QAAA6B,OAAAf,CAAAA;IACH;EAAA;AAAA;", "names": ["setCustomValidity", "ref", "fieldPath", "errors", "error", "get", "message", "reportValidity", "validateFieldsNatively", "options", "_loop", "field", "fields", "refs", "for<PERSON>ach", "toNestErrors", "shouldUseNativeValidation", "fieldErrors", "path", "Object", "assign", "isNameInFieldArray", "names", "keys", "fieldArrayErrors", "set", "name", "some", "n", "startsWith", "yupResolver", "schema", "schemaOptions", "resolverOptions", "values", "context", "options", "Promise", "resolve", "console", "warn", "mode", "Object", "assign", "abort<PERSON><PERSON><PERSON>", "then", "result", "shouldUseNativeValidation", "validateFieldsNatively", "raw", "errors", "e", "inner", "toNestErrors", "error", "validateAllFieldCriteria", "criteriaMode", "reduce", "previous", "path", "message", "type", "types", "messages", "appendErrors", "concat", "reject"]}