import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
import capitalize from '@mui/utils/capitalize';
import responsivePropType from './responsivePropType';
import { handleBreakpoints } from './breakpoints';
export function getPath(obj, path) {
  var checkVars = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
  if (!path || typeof path !== 'string') {
    return null;
  }

  // Check if CSS variables are used
  if (obj && obj.vars && checkVars) {
    var val = "vars.".concat(path).split('.').reduce(function (acc, item) {
      return acc && acc[item] ? acc[item] : null;
    }, obj);
    if (val != null) {
      return val;
    }
  }
  return path.split('.').reduce(function (acc, item) {
    if (acc && acc[item] != null) {
      return acc[item];
    }
    return null;
  }, obj);
}
export function getStyleValue(themeMapping, transform, propValueFinal) {
  var userValue = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : propValueFinal;
  var value;
  if (typeof themeMapping === 'function') {
    value = themeMapping(propValueFinal);
  } else if (Array.isArray(themeMapping)) {
    value = themeMapping[propValueFinal] || userValue;
  } else {
    value = getPath(themeMapping, propValueFinal) || userValue;
  }
  if (transform) {
    value = transform(value, userValue, themeMapping);
  }
  return value;
}
function style(options) {
  var prop = options.prop,
    _options$cssProperty = options.cssProperty,
    cssProperty = _options$cssProperty === void 0 ? options.prop : _options$cssProperty,
    themeKey = options.themeKey,
    transform = options.transform; // false positive
  // eslint-disable-next-line react/function-component-definition
  var fn = function fn(props) {
    if (props[prop] == null) {
      return null;
    }
    var propValue = props[prop];
    var theme = props.theme;
    var themeMapping = getPath(theme, themeKey) || {};
    var styleFromPropValue = function styleFromPropValue(propValueFinal) {
      var value = getStyleValue(themeMapping, transform, propValueFinal);
      if (propValueFinal === value && typeof propValueFinal === 'string') {
        // Haven't found value
        value = getStyleValue(themeMapping, transform, "".concat(prop).concat(propValueFinal === 'default' ? '' : capitalize(propValueFinal)), propValueFinal);
      }
      if (cssProperty === false) {
        return value;
      }
      return _defineProperty({}, cssProperty, value);
    };
    return handleBreakpoints(props, propValue, styleFromPropValue);
  };
  fn.propTypes = process.env.NODE_ENV !== 'production' ? _defineProperty({}, prop, responsivePropType) : {};
  fn.filterProps = [prop];
  return fn;
}
export default style;