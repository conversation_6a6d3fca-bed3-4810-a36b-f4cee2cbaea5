"use strict";function n(n){return n&&"object"==typeof n&&"default"in n?n.default:n}Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=n(e),r=require("react-dom"),i=n(require("clsx")),o=require("goober");function a(n,e){for(var t=0;t<e.length;t++){var r=e[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(n,r.key,r)}}function s(n,e,t){return e&&a(n.prototype,e),t&&a(n,t),n}function u(){return(u=Object.assign||function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n}).apply(this,arguments)}function c(n,e){n.prototype=Object.create(e.prototype),n.prototype.constructor=n,n.__proto__=e}function l(n,e){if(null==n)return{};var t,r,i={},o=Object.keys(n);for(r=0;r<o.length;r++)e.indexOf(t=o[r])>=0||(i[t]=n[t]);return i}function d(n){if(void 0===n)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n}var f=function(){return""},p=t.createContext({enqueueSnackbar:f,closeSnackbar:f}),m=function(n){return n.charAt(0).toUpperCase()+n.slice(1)},h=function(n){return""+m(n.vertical)+m(n.horizontal)},x=function(n){return!!n||0===n},g=function(n){function e(e){var t;t=n.call(this,e)||this;var r,i=e.appear;return t.appearStatus=null,e.in?i?(r="exited",t.appearStatus="entering"):r="entered":r=e.unmountOnExit||e.mountOnEnter?"unmounted":"exited",t.state={status:r},t.nextCallback=null,t}c(e,n),e.getDerivedStateFromProps=function(n,e){return n.in&&"unmounted"===e.status?{status:"exited"}:null};var t=e.prototype;return t.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},t.componentDidUpdate=function(n){var e=null;if(n!==this.props){var t=this.state.status;this.props.in?"entering"!==t&&"entered"!==t&&(e="entering"):"entering"!==t&&"entered"!==t||(e="exiting")}this.updateStatus(!1,e)},t.componentWillUnmount=function(){this.cancelNextCallback()},t.getTimeouts=function(){var n=this.props.timeout,e=n,t=n;return null!=n&&"number"!=typeof n&&"string"!=typeof n&&(t=n.exit,e=n.enter),{exit:t,enter:e}},t.updateStatus=function(n,e){void 0===n&&(n=!1),null!==e?(this.cancelNextCallback(),"entering"===e?this.performEnter(n):this.performExit()):this.props.unmountOnExit&&"exited"===this.state.status&&this.setState({status:"unmounted"})},t.performEnter=function(n){var e=this,t=this.props.enter,r=n,i=this.getTimeouts();n||t?(this.props.onEnter&&this.props.onEnter(this.node,r),this.safeSetState({status:"entering"},(function(){e.props.onEntering&&e.props.onEntering(e.node,r),e.onTransitionEnd(i.enter,(function(){e.safeSetState({status:"entered"},(function(){e.props.onEntered&&e.props.onEntered(e.node,r)}))}))}))):this.safeSetState({status:"entered"},(function(){e.props.onEntered&&e.props.onEntered(e.node,r)}))},t.performExit=function(){var n=this,e=this.props.exit,t=this.getTimeouts();e?(this.props.onExit&&this.props.onExit(this.node),this.safeSetState({status:"exiting"},(function(){n.props.onExiting&&n.props.onExiting(n.node),n.onTransitionEnd(t.exit,(function(){n.safeSetState({status:"exited"},(function(){n.props.onExited&&n.props.onExited(n.node)}))}))}))):this.safeSetState({status:"exited"},(function(){n.props.onExited&&n.props.onExited(n.node)}))},t.cancelNextCallback=function(){null!==this.nextCallback&&this.nextCallback.cancel&&(this.nextCallback.cancel(),this.nextCallback=null)},t.safeSetState=function(n,e){e=this.setNextCallback(e),this.setState(n,e)},t.setNextCallback=function(n){var e=this,t=!0;return this.nextCallback=function(){t&&(t=!1,e.nextCallback=null,n())},this.nextCallback.cancel=function(){t=!1},this.nextCallback},t.onTransitionEnd=function(n,e){this.setNextCallback(e),!this.node||null==n&&!this.props.addEndListener?setTimeout(this.nextCallback,0):(this.props.addEndListener&&this.props.addEndListener(this.node,this.nextCallback),null!=n&&setTimeout(this.nextCallback,n))},t.render=function(){var n=this.state.status;if("unmounted"===n)return null;var e=this.props;return(0,e.children)(n,l(e,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]))},s(e,[{key:"node",get:function(){var n,e=null===(n=this.props.nodeRef)||void 0===n?void 0:n.current;if(!e)throw new Error("notistack - Custom snackbar is not refForwarding");return e}}]),e}(t.Component);function v(){}function E(n,e){"function"==typeof n?n(e):n&&(n.current=e)}function b(n,t){return e.useMemo((function(){return null==n&&null==t?null:function(e){E(n,e),E(t,e)}}),[n,t])}function k(n){var e=n.timeout,t=n.style,r=void 0===t?{}:t;return{duration:"object"==typeof e?e[n.mode]||0:e,easing:r.transitionTimingFunction,delay:r.transitionDelay}}g.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:v,onEntering:v,onEntered:v,onExit:v,onExiting:v,onExited:v};var y=function(n){n.scrollTop=n.scrollTop},C=function(n){return Math.round(n)+"ms"};function w(n,e){void 0===n&&(n=["all"]);var t=e||{},r=t.duration,i=void 0===r?300:r,o=t.easing,a=void 0===o?"cubic-bezier(0.4, 0, 0.2, 1)":o,s=t.delay,u=void 0===s?0:s;return(Array.isArray(n)?n:[n]).map((function(n){var e="string"==typeof i?i:C(i),t="string"==typeof u?u:C(u);return n+" "+e+" "+a+" "+t})).join(",")}function S(n){return function(n){return n&&n.ownerDocument||document}(n).defaultView||window}function O(n,e){if(e){var t=function(n,e){var t,r=e.getBoundingClientRect(),i=S(e);if(e.fakeTransform)t=e.fakeTransform;else{var o=i.getComputedStyle(e);t=o.getPropertyValue("-webkit-transform")||o.getPropertyValue("transform")}var a=0,s=0;if(t&&"none"!==t&&"string"==typeof t){var u=t.split("(")[1].split(")")[0].split(",");a=parseInt(u[4],10),s=parseInt(u[5],10)}switch(n){case"left":return"translateX("+(i.innerWidth+a-r.left)+"px)";case"right":return"translateX(-"+(r.left+r.width-a)+"px)";case"up":return"translateY("+(i.innerHeight+s-r.top)+"px)";default:return"translateY(-"+(r.top+r.height-s)+"px)"}}(n,e);t&&(e.style.webkitTransform=t,e.style.transform=t)}}var T=e.forwardRef((function(n,t){var r=n.children,i=n.direction,o=void 0===i?"down":i,a=n.in,s=n.style,c=n.timeout,d=void 0===c?0:c,f=n.onEnter,p=n.onEntered,m=n.onExit,h=n.onExited,x=l(n,["children","direction","in","style","timeout","onEnter","onEntered","onExit","onExited"]),v=e.useRef(null),E=b(r.ref,v),C=b(E,t),T=e.useCallback((function(){v.current&&O(o,v.current)}),[o]);return e.useEffect((function(){if(!a&&"down"!==o&&"right"!==o){var n=function(n,e){var t;function r(){for(var r=this,i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];var s=function(){n.apply(r,o)};clearTimeout(t),t=setTimeout(s,e)}return void 0===e&&(e=166),r.clear=function(){clearTimeout(t)},r}((function(){v.current&&O(o,v.current)})),e=S(v.current);return e.addEventListener("resize",n),function(){n.clear(),e.removeEventListener("resize",n)}}}),[o,a]),e.useEffect((function(){a||T()}),[a,T]),e.createElement(g,Object.assign({appear:!0,nodeRef:v,onEnter:function(n,e){O(o,n),y(n),f&&f(n,e)},onEntered:p,onEntering:function(n){var e=k({timeout:d,mode:"enter",style:u({},s,{transitionTimingFunction:(null==s?void 0:s.transitionTimingFunction)||"cubic-bezier(0.0, 0, 0.2, 1)"})});n.style.webkitTransition=w("-webkit-transform",e),n.style.transition=w("transform",e),n.style.webkitTransform="none",n.style.transform="none"},onExit:function(n){var e=k({timeout:d,mode:"exit",style:u({},s,{transitionTimingFunction:(null==s?void 0:s.transitionTimingFunction)||"cubic-bezier(0.4, 0, 0.6, 1)"})});n.style.webkitTransition=w("-webkit-transform",e),n.style.transition=w("transform",e),O(o,n),m&&m(n)},onExited:function(n){n.style.webkitTransition="",n.style.transition="",h&&h(n)},in:a,timeout:d},x),(function(n,t){return e.cloneElement(r,u({ref:C,style:u({visibility:"exited"!==n||a?void 0:"hidden"},s,{},r.props.style)},t))}))}));T.displayName="Slide";var L=function(n){return t.createElement("svg",Object.assign({viewBox:"0 0 24 24",focusable:"false",style:{fontSize:20,marginInlineEnd:8,userSelect:"none",width:"1em",height:"1em",display:"inline-block",fill:"currentColor",flexShrink:0}},n))},D=function(){return t.createElement(L,null,t.createElement("path",{d:"M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41\n        10.59L10 14.17L17.59 6.58L19 8L10 17Z"}))},N=function(){return t.createElement(L,null,t.createElement("path",{d:"M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z"}))},q=function(){return t.createElement(L,null,t.createElement("path",{d:"M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,\n        6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,\n        13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z"}))},M=function(){return t.createElement(L,null,t.createElement("path",{d:"M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,\n        0 22,12A10,10 0 0,0 12,2Z"}))},R={maxSnack:3,persist:!1,hideIconVariant:!1,disableWindowBlurListener:!1,variant:"default",autoHideDuration:5e3,iconVariant:{default:void 0,success:t.createElement(D,null),warning:t.createElement(N,null),error:t.createElement(q,null),info:t.createElement(M,null)},anchorOrigin:{vertical:"bottom",horizontal:"left"},TransitionComponent:T,transitionDuration:{enter:225,exit:195}};function H(n){return Object.entries(n).reduce((function(n,e){var t;return u({},n,((t={})[e[0]]=o.css(e[1]),t))}),{})}var j=function(n){return"notistack-MuiContent-"+n},P=H({root:{height:0},entered:{height:"auto"}}),V=e.forwardRef((function(n,t){var r=n.children,o=n.in,a=n.onExited,s=e.useRef(null),c=e.useRef(null),l=b(t,c),d=function(){return s.current?s.current.clientHeight:0};return e.createElement(g,{in:o,unmountOnExit:!0,onEnter:function(n){n.style.height="0px"},onEntered:function(n){n.style.height="auto"},onEntering:function(n){var e=d(),t=k({timeout:175,mode:"enter"}),r=t.duration,i=t.easing;n.style.transitionDuration="string"==typeof r?r:r+"ms",n.style.height=e+"px",n.style.transitionTimingFunction=i||""},onExit:function(n){n.style.height=d()+"px"},onExited:a,onExiting:function(n){y(n);var e=k({timeout:175,mode:"exit"}),t=e.duration,r=e.easing;n.style.transitionDuration="string"==typeof t?t:t+"ms",n.style.height="0px",n.style.transitionTimingFunction=r||""},nodeRef:c,timeout:175},(function(n,t){return e.createElement("div",Object.assign({ref:l,className:i(P.root,"entered"===n&&P.entered),style:u({pointerEvents:"all",overflow:"hidden",minHeight:"0px",transition:w("height")},"entered"===n&&{overflow:"visible"},{},"exited"===n&&!o&&{visibility:"hidden"})},t),e.createElement("div",{ref:s,className:"notistack-CollapseWrapper",style:{display:"flex",width:"100%"}},r))}))}));V.displayName="Collapse";var W={right:"left",left:"right",bottom:"up",top:"down"},A=function(n){return"anchorOrigin"+h(n)},I=function(){};function z(n,e){return n.reduce((function(n,t){return null==t?n:function(){for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];var a=[].concat(i);e&&-1===a.indexOf(e)&&a.push(e),n.apply(this,a),t.apply(this,a)}}),I)}var B="undefined"!=typeof window?e.useLayoutEffect:e.useEffect;function F(n){var t=e.useRef(n);return B((function(){t.current=n})),e.useCallback((function(){return t.current.apply(void 0,arguments)}),[])}var _,X=e.forwardRef((function(n,t){var r=n.children,o=n.className,a=n.autoHideDuration,s=n.disableWindowBlurListener,u=void 0!==s&&s,c=n.onClose,l=n.id,d=n.open,f=n.SnackbarProps,p=void 0===f?{}:f,m=e.useRef(),h=F((function(){c&&c.apply(void 0,arguments)})),x=F((function(n){c&&null!=n&&(m.current&&clearTimeout(m.current),m.current=setTimeout((function(){h(null,"timeout",l)}),n))}));e.useEffect((function(){return d&&x(a),function(){m.current&&clearTimeout(m.current)}}),[d,a,x]);var g=function(){m.current&&clearTimeout(m.current)},v=e.useCallback((function(){null!=a&&x(.5*a)}),[a,x]);return e.useEffect((function(){if(!u&&d)return window.addEventListener("focus",v),window.addEventListener("blur",g),function(){window.removeEventListener("focus",v),window.removeEventListener("blur",g)}}),[u,v,d]),e.createElement("div",Object.assign({ref:t},p,{className:i("notistack-Snackbar",o),onMouseEnter:function(n){p.onMouseEnter&&p.onMouseEnter(n),g()},onMouseLeave:function(n){p.onMouseLeave&&p.onMouseLeave(n),v()}}),r)}));X.displayName="Snackbar";var Z=H({root:(_={display:"flex",flexWrap:"wrap",flexGrow:1},_["@media (min-width:600px)"]={flexGrow:"initial",minWidth:"288px"},_)}),Q=e.forwardRef((function(n,e){var r=n.className,o=l(n,["className"]);return t.createElement("div",Object.assign({ref:e,className:i(Z.root,r)},o))}));Q.displayName="SnackbarContent";var U=H({root:{backgroundColor:"#313131",fontSize:"0.875rem",lineHeight:1.43,letterSpacing:"0.01071em",color:"#fff",alignItems:"center",padding:"6px 16px",borderRadius:"4px",boxShadow:"0px 3px 5px -1px rgba(0,0,0,0.2),0px 6px 10px 0px rgba(0,0,0,0.14),0px 1px 18px 0px rgba(0,0,0,0.12)"},lessPadding:{paddingLeft:"20px"},default:{backgroundColor:"#313131"},success:{backgroundColor:"#43a047"},error:{backgroundColor:"#d32f2f"},warning:{backgroundColor:"#ff9800"},info:{backgroundColor:"#2196f3"},message:{display:"flex",alignItems:"center",padding:"8px 0"},action:{display:"flex",alignItems:"center",marginLeft:"auto",paddingLeft:"16px",marginRight:"-8px"}}),G=e.forwardRef((function(n,e){var r=n.message,o=n.variant,a=n.hideIconVariant,s=n.style,u=n.className,c=n.iconVariant[o],l=n.action;return"function"==typeof l&&(l=l(n.id)),t.createElement(Q,{ref:e,role:"alert","aria-describedby":"notistack-snackbar",style:s,className:i("notistack-MuiContent",j(o),U.root,U[o],u,!a&&c&&U.lessPadding)},t.createElement("div",{id:"notistack-snackbar",className:U.message},a?null:c,r),l&&t.createElement("div",{className:U.action},l))}));G.displayName="MaterialDesignContent";var Y,J,K,$,nn,en=e.memo(G),tn=H({wrappedRoot:{width:"100%",position:"relative",transform:"translateX(0)",top:0,right:0,bottom:0,left:0,minWidth:"288px"}}),rn=function(n){var r=e.useRef(),o=e.useState(!0),a=o[0],s=o[1],c=z([n.snack.onClose,n.onClose]),d=e.useCallback((function(){r.current=setTimeout((function(){s((function(n){return!n}))}),125)}),[]);e.useEffect((function(){return function(){r.current&&clearTimeout(r.current)}}),[]);var f,p=n.snack,m=n.classes,h=n.Component,x=void 0===h?en:h,g=e.useMemo((function(){return function(n){void 0===n&&(n={});var e={containerRoot:!0,containerAnchorOriginTopCenter:!0,containerAnchorOriginBottomCenter:!0,containerAnchorOriginTopRight:!0,containerAnchorOriginBottomRight:!0,containerAnchorOriginTopLeft:!0,containerAnchorOriginBottomLeft:!0};return Object.keys(n).filter((function(n){return!e[n]})).reduce((function(e,t){var r;return u({},e,((r={})[t]=n[t],r))}),{})}(m)}),[m]),v=p.open,E=p.SnackbarProps,b=p.TransitionComponent,k=p.TransitionProps,y=p.transitionDuration,C=p.disableWindowBlurListener,w=p.content,S=l(p,["open","SnackbarProps","TransitionComponent","TransitionProps","transitionDuration","disableWindowBlurListener","content","entered","requestClose","onEnter","onEntered","onExit","onExited"]),O=u({direction:(f=S.anchorOrigin,"center"!==f.horizontal?W[f.horizontal]:W[f.vertical]),timeout:y},k),T=w;"function"==typeof T&&(T=T(S.id,S.message));var L=["onEnter","onEntered","onExit","onExited"].reduce((function(e,t){var r;return u({},e,((r={})[t]=z([n.snack[t],n[t]],S.id),r))}),{});return t.createElement(V,{in:a,onExited:L.onExited},t.createElement(X,{open:v,id:S.id,disableWindowBlurListener:C,autoHideDuration:S.autoHideDuration,className:i(tn.wrappedRoot,g.root,g[A(S.anchorOrigin)]),SnackbarProps:E,onClose:c},t.createElement(b,Object.assign({},O,{appear:!0,in:v,onExit:L.onExit,onExited:d,onEnter:L.onEnter,onEntered:z([L.onEntered,function(){n.snack.requestClose&&c(null,"instructed",n.snack.id)}],S.id)}),T||t.createElement(x,Object.assign({},S)))))},on=H({root:(Y={boxSizing:"border-box",display:"flex",maxHeight:"100%",position:"fixed",zIndex:1400,height:"auto",width:"auto",transition:w(["top","right","bottom","left","max-width"],{duration:300,easing:"ease"}),pointerEvents:"none"},Y[".notistack-CollapseWrapper"]={padding:"6px 0px",transition:"padding 300ms ease 0ms"},Y.maxWidth="calc(100% - 40px)",Y["@media (max-width:599.95px)"]={width:"100%",maxWidth:"calc(100% - 32px)"},Y),rootDense:(J={},J[".notistack-CollapseWrapper"]={padding:"2px 0px"},J),top:{top:"14px",flexDirection:"column"},bottom:{bottom:"14px",flexDirection:"column-reverse"},left:(K={left:"20px"},K["@media (min-width:600px)"]={alignItems:"flex-start"},K["@media (max-width:599.95px)"]={left:"16px"},K),right:($={right:"20px"},$["@media (min-width:600px)"]={alignItems:"flex-end"},$["@media (max-width:599.95px)"]={right:"16px"},$),center:(nn={left:"50%",transform:"translateX(-50%)"},nn["@media (min-width:600px)"]={alignItems:"center"},nn)}),an=e.memo((function(n){var e=n.classes,r=void 0===e?{}:e,o=n.anchorOrigin,a=n.dense,s=n.children,u=i("notistack-SnackbarContainer",on[o.vertical],on[o.horizontal],on.root,r.containerRoot,r["containerAnchorOrigin"+h(o)],a&&on.rootDense);return t.createElement("div",{className:u},s)})),sn=function(n){return!("string"==typeof n||e.isValidElement(n))},un=function(n){function e(e){var t;return(t=n.call(this,e)||this).enqueueSnackbar=function(n,e){if(void 0===e&&(e={}),null==n)throw new Error("enqueueSnackbar called with invalid argument");var r=sn(n)?n:e,o=sn(n)?n.message:n,a=r.key,s=r.preventDuplicate,c=l(r,["key","preventDuplicate"]),d=x(a),f=d?a:(new Date).getTime()+Math.random(),p=function(n,e){return function(t,r){return void 0===r&&(r=!1),r?u({},R[t],{},e[t],{},n[t]):"autoHideDuration"===t?(o=e.autoHideDuration,(a=function(n){return"number"==typeof n||null===n})(i=n.autoHideDuration)?i:a(o)?o:R.autoHideDuration):"transitionDuration"===t?function(n,e){var t=function(n,e){return e.some((function(e){return typeof n===e}))};return t(n,["string","number"])?n:t(n,["object"])?u({},R.transitionDuration,{},t(e,["object"])&&e,{},n):t(e,["string","number"])?e:t(e,["object"])?u({},R.transitionDuration,{},e):R.transitionDuration}(n.transitionDuration,e.transitionDuration):n[t]||e[t]||R[t];var i,o,a}}(c,t.props),m=u({id:f},c,{message:o,open:!0,entered:!1,requestClose:!1,persist:p("persist"),action:p("action"),content:p("content"),variant:p("variant"),anchorOrigin:p("anchorOrigin"),disableWindowBlurListener:p("disableWindowBlurListener"),autoHideDuration:p("autoHideDuration"),hideIconVariant:p("hideIconVariant"),TransitionComponent:p("TransitionComponent"),transitionDuration:p("transitionDuration"),TransitionProps:p("TransitionProps",!0),iconVariant:p("iconVariant",!0),style:p("style",!0),SnackbarProps:p("SnackbarProps",!0),className:i(t.props.className,c.className)});return m.persist&&(m.autoHideDuration=void 0),t.setState((function(n){if(void 0===s&&t.props.preventDuplicate||s){var e=function(n){return d?n.id===f:n.message===o},r=n.queue.findIndex(e)>-1,i=n.snacks.findIndex(e)>-1;if(r||i)return n}return t.handleDisplaySnack(u({},n,{queue:[].concat(n.queue,[m])}))})),f},t.handleDisplaySnack=function(n){return n.snacks.length>=t.maxSnack?t.handleDismissOldest(n):t.processQueue(n)},t.processQueue=function(n){var e=n.queue;return e.length>0?u({},n,{snacks:[].concat(n.snacks,[e[0]]),queue:e.slice(1,e.length)}):n},t.handleDismissOldest=function(n){if(n.snacks.some((function(n){return!n.open||n.requestClose})))return n;var e=!1,r=!1;n.snacks.reduce((function(n,e){return n+(e.open&&e.persist?1:0)}),0)===t.maxSnack&&(r=!0);var i=n.snacks.map((function(n){return e||n.persist&&!r?u({},n):(e=!0,n.entered?(n.onClose&&n.onClose(null,"maxsnack",n.id),t.props.onClose&&t.props.onClose(null,"maxsnack",n.id),u({},n,{open:!1})):u({},n,{requestClose:!0}))}));return u({},n,{snacks:i})},t.handleEnteredSnack=function(n,e,r){if(!x(r))throw new Error("handleEnteredSnack Cannot be called with undefined key");t.setState((function(n){return{snacks:n.snacks.map((function(n){return n.id===r?u({},n,{entered:!0}):u({},n)}))}}))},t.handleCloseSnack=function(n,e,r){t.props.onClose&&t.props.onClose(n,e,r);var i=void 0===r;t.setState((function(n){var e=n.queue;return{snacks:n.snacks.map((function(n){return i||n.id===r?u({},n,n.entered?{open:!1}:{requestClose:!0}):u({},n)})),queue:e.filter((function(n){return n.id!==r}))}}))},t.closeSnackbar=function(n){var e=t.state.snacks.find((function(e){return e.id===n}));x(n)&&e&&e.onClose&&e.onClose(null,"instructed",n),t.handleCloseSnack(null,"instructed",n)},t.handleExitedSnack=function(n,e){if(!x(e))throw new Error("handleExitedSnack Cannot be called with undefined key");t.setState((function(n){var r=t.processQueue(u({},n,{snacks:n.snacks.filter((function(n){return n.id!==e}))}));return 0===r.queue.length?r:t.handleDismissOldest(r)}))},exports.enqueueSnackbar=t.enqueueSnackbar,exports.closeSnackbar=t.closeSnackbar,t.state={snacks:[],queue:[],contextValue:{enqueueSnackbar:t.enqueueSnackbar.bind(d(t)),closeSnackbar:t.closeSnackbar.bind(d(t))}},t}return c(e,n),e.prototype.render=function(){var n=this,e=this.state.contextValue,i=this.props,o=i.domRoot,a=i.children,s=i.dense,c=void 0!==s&&s,l=i.Components,d=void 0===l?{}:l,f=i.classes,m=this.state.snacks.reduce((function(n,e){var t,r=h(e.anchorOrigin);return u({},n,((t={})[r]=[].concat(n[r]||[],[e]),t))}),{}),x=Object.keys(m).map((function(e){var r=m[e];return t.createElement(an,{key:e,dense:c,anchorOrigin:r[0].anchorOrigin,classes:f},r.map((function(e){return t.createElement(rn,{key:e.id,snack:e,classes:f,Component:d[e.variant],onClose:n.handleCloseSnack,onEnter:n.props.onEnter,onExit:n.props.onExit,onExited:z([n.handleExitedSnack,n.props.onExited],e.id),onEntered:z([n.handleEnteredSnack,n.props.onEntered],e.id)})})))}));return t.createElement(p.Provider,{value:e},a,o?r.createPortal(x,o):x)},s(e,[{key:"maxSnack",get:function(){return this.props.maxSnack||R.maxSnack}}]),e}(e.Component);exports.MaterialDesignContent=en,exports.SnackbarContent=Q,exports.SnackbarProvider=un,exports.Transition=g,exports.useSnackbar=function(){return e.useContext(p)};
//# sourceMappingURL=notistack.cjs.production.min.js.map
