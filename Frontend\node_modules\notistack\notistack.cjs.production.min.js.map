{"version": 3, "file": "notistack.cjs.production.min.js", "sources": ["../src/SnackbarContext.ts", "../src/utils/index.ts", "../src/transitions/Transition/Transition.tsx", "../src/transitions/useForkRef.ts", "../src/transitions/getTransitionProps.ts", "../src/transitions/utils.ts", "../src/transitions/createTransition.ts", "../src/transitions/Slide/Slide.tsx", "../src/utils/defaultIconVariants.tsx", "../src/SnackbarProvider/merger.ts", "../src/utils/styles.ts", "../src/transitions/Collapse/Collapse.tsx", "../src/SnackbarItem/utils.ts", "../src/utils/createChainedFunction.ts", "../src/utils/useEventCallback.ts", "../src/SnackbarItem/Snackbar.tsx", "../src/SnackbarContent/SnackbarContent.tsx", "../src/ui/MaterialDesignContent/MaterialDesignContent.tsx", "../src/SnackbarItem/SnackbarItem.tsx", "../src/SnackbarContainer/SnackbarContainer.tsx", "../src/SnackbarProvider/SnackbarProvider.tsx", "../src/useSnackbar.ts"], "sourcesContent": ["import React from 'react';\nimport { ProviderContext } from './types';\n\nconst noOp = () => {\n    return '';\n};\n\nexport default React.createContext<ProviderContext>({\n    enqueueSnackbar: noOp,\n    closeSnackbar: noOp,\n});\n", "import { InternalSnack } from '../types';\n\nexport const breakpoints = {\n    downXs: '@media (max-width:599.95px)',\n    upSm: '@media (min-width:600px)',\n};\n\nconst capitalise = (text: string): string => text.charAt(0).toUpperCase() + text.slice(1);\n\nexport const originKeyExtractor = (anchor: InternalSnack['anchorOrigin']): string =>\n    `${capitalise(anchor.vertical)}${capitalise(anchor.horizontal)}`;\n\nexport const isDefined = (value: string | null | undefined | number): boolean => !!value || value === 0;\n", "/**\n * BSD 3-Clause License\n *\n * Copyright (c) 2018, React Community\n * Forked from React (https://github.com/facebook/react) Copyright 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * * Redistributions of source code must retain the above copyright notice, this\n * list of conditions and the following disclaimer.\n *\n * * Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * * Neither the name of the copyright holder nor the names of its\n * contributors may be used to endorse or promote products derived from\n * this software without specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE\n * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL\n * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\n * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\n * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,\n * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\nimport React from 'react';\nimport { TransitionComponentProps, TransitionStatus } from '../../types';\n\nconst UNMOUNTED = 'unmounted';\nconst EXITED = 'exited';\nconst ENTERING = 'entering';\nconst ENTERED = 'entered';\nconst EXITING = 'exiting';\n\ninterface State {\n    status: TransitionStatus;\n}\n\ninterface NextCallback {\n    (): void;\n    cancel?: () => void;\n}\n\nclass Transition extends React.Component<TransitionComponentProps, State> {\n    appearStatus: TransitionStatus | null;\n\n    nextCallback: NextCallback | null;\n\n    constructor(props: TransitionComponentProps) {\n        super(props);\n\n        const { appear } = props;\n\n        let initialStatus: TransitionStatus;\n\n        this.appearStatus = null;\n\n        if (props.in) {\n            if (appear) {\n                initialStatus = EXITED;\n                this.appearStatus = ENTERING;\n            } else {\n                initialStatus = ENTERED;\n            }\n        } else if (props.unmountOnExit || props.mountOnEnter) {\n            initialStatus = UNMOUNTED;\n        } else {\n            initialStatus = EXITED;\n        }\n\n        this.state = { status: initialStatus };\n\n        this.nextCallback = null;\n    }\n\n    static getDerivedStateFromProps({ in: nextIn }: TransitionComponentProps, prevState: State) {\n        if (nextIn && prevState.status === UNMOUNTED) {\n            return { status: EXITED };\n        }\n        return null;\n    }\n\n    componentDidMount() {\n        this.updateStatus(true, this.appearStatus);\n    }\n\n    componentDidUpdate(prevProps: TransitionComponentProps) {\n        let nextStatus: TransitionStatus | null = null;\n        if (prevProps !== this.props) {\n            const { status } = this.state;\n\n            if (this.props.in) {\n                if (status !== ENTERING && status !== ENTERED) {\n                    nextStatus = ENTERING;\n                }\n            } else if (status === ENTERING || status === ENTERED) {\n                nextStatus = EXITING;\n            }\n        }\n        this.updateStatus(false, nextStatus);\n    }\n\n    componentWillUnmount() {\n        this.cancelNextCallback();\n    }\n\n    getTimeouts(): { exit: number; enter: number } {\n        const { timeout } = this.props;\n        let enter = timeout;\n        let exit = timeout;\n\n        if (timeout != null && typeof timeout !== 'number' && typeof timeout !== 'string') {\n            exit = timeout.exit;\n            enter = timeout.enter;\n        }\n        return {\n            exit: exit as number,\n            enter: enter as number,\n        };\n    }\n\n    updateStatus(mounting = false, nextStatus: TransitionStatus | null) {\n        if (nextStatus !== null) {\n            this.cancelNextCallback();\n\n            if (nextStatus === ENTERING) {\n                this.performEnter(mounting);\n            } else {\n                this.performExit();\n            }\n        } else if (this.props.unmountOnExit && this.state.status === EXITED) {\n            this.setState({ status: UNMOUNTED });\n        }\n    }\n\n    get node() {\n        const node = this.props.nodeRef?.current;\n        if (!node) {\n            throw new Error('notistack - Custom snackbar is not refForwarding');\n        }\n        return node;\n    }\n\n    performEnter(mounting: boolean) {\n        const { enter } = this.props;\n        const isAppearing = mounting;\n\n        const timeouts = this.getTimeouts();\n\n        if (!mounting && !enter) {\n            this.safeSetState({ status: ENTERED }, () => {\n                if (this.props.onEntered) {\n                    this.props.onEntered(this.node, isAppearing);\n                }\n            });\n            return;\n        }\n\n        if (this.props.onEnter) {\n            this.props.onEnter(this.node, isAppearing);\n        }\n\n        this.safeSetState({ status: ENTERING }, () => {\n            if (this.props.onEntering) {\n                this.props.onEntering(this.node, isAppearing);\n            }\n\n            this.onTransitionEnd(timeouts.enter, () => {\n                this.safeSetState({ status: ENTERED }, () => {\n                    if (this.props.onEntered) {\n                        this.props.onEntered(this.node, isAppearing);\n                    }\n                });\n            });\n        });\n    }\n\n    performExit() {\n        const { exit } = this.props;\n        const timeouts = this.getTimeouts();\n\n        // no exit animation skip right to EXITED\n        if (!exit) {\n            this.safeSetState({ status: EXITED }, () => {\n                if (this.props.onExited) {\n                    this.props.onExited(this.node);\n                }\n            });\n            return;\n        }\n\n        if (this.props.onExit) {\n            this.props.onExit(this.node);\n        }\n\n        this.safeSetState({ status: EXITING }, () => {\n            if (this.props.onExiting) {\n                this.props.onExiting(this.node);\n            }\n\n            this.onTransitionEnd(timeouts.exit, () => {\n                this.safeSetState({ status: EXITED }, () => {\n                    if (this.props.onExited) {\n                        this.props.onExited(this.node);\n                    }\n                });\n            });\n        });\n    }\n\n    cancelNextCallback() {\n        if (this.nextCallback !== null && this.nextCallback.cancel) {\n            this.nextCallback.cancel();\n            this.nextCallback = null;\n        }\n    }\n\n    safeSetState(nextState: State, callback: () => void) {\n        callback = this.setNextCallback(callback);\n        this.setState(nextState, callback);\n    }\n\n    setNextCallback(callback: () => void) {\n        let active = true;\n\n        this.nextCallback = () => {\n            if (active) {\n                active = false;\n                this.nextCallback = null;\n\n                callback();\n            }\n        };\n\n        (this.nextCallback as NextCallback).cancel = () => {\n            active = false;\n        };\n\n        return this.nextCallback;\n    }\n\n    onTransitionEnd(timeout: number, handler: () => void) {\n        this.setNextCallback(handler);\n        const doesNotHaveTimeoutOrListener = timeout == null && !this.props.addEndListener;\n        if (!this.node || doesNotHaveTimeoutOrListener) {\n            setTimeout(this.nextCallback as NextCallback, 0);\n            return;\n        }\n\n        if (this.props.addEndListener) {\n            this.props.addEndListener(this.node, this.nextCallback as NextCallback);\n        }\n\n        if (timeout != null) {\n            setTimeout(this.nextCallback as NextCallback, timeout);\n        }\n    }\n\n    render() {\n        const { status } = this.state;\n\n        if (status === UNMOUNTED) {\n            return null;\n        }\n\n        const {\n            children,\n            // filter props for `Transition`\n            in: _in,\n            mountOnEnter: _mountOnEnter,\n            unmountOnExit: _unmountOnExit,\n            appear: _appear,\n            enter: _enter,\n            exit: _exit,\n            timeout: _timeout,\n            addEndListener: _addEndListener,\n            onEnter: _onEnter,\n            onEntering: _onEntering,\n            onEntered: _onEntered,\n            onExit: _onExit,\n            onExiting: _onExiting,\n            onExited: _onExited,\n            nodeRef: _nodeRef,\n            ...childProps\n        } = this.props;\n\n        return children(status, childProps);\n    }\n}\n\nfunction noop() {\n    //\n}\n\n(Transition as any).defaultProps = {\n    in: false,\n    mountOnEnter: false,\n    unmountOnExit: false,\n    appear: false,\n    enter: true,\n    exit: true,\n\n    onEnter: noop,\n    onEntering: noop,\n    onEntered: noop,\n\n    onExit: noop,\n    onExiting: noop,\n    onExited: noop,\n};\n\nexport default Transition;\n", "/**\n * Credit to MUI team @ https://mui.com\n */\nimport * as React from 'react';\n\n/**\n * passes {value} to {ref}\n *\n * Useful if you want to expose the ref of an inner component to the public API\n * while still using it inside the component.\n * @param ref A ref callback or ref object. If anything falsy, this is a no-op.\n */\nfunction setRef<T>(\n    ref: React.MutableRefObject<T | null> | ((instance: T | null) => void) | null | undefined,\n    value: T | null\n): void {\n    if (typeof ref === 'function') {\n        ref(value);\n    } else if (ref) {\n        ref.current = value;\n    }\n}\n\nexport default function useForkRef<Instance>(\n    refA: React.Ref<Instance> | null | undefined,\n    refB: React.Ref<Instance> | null | undefined\n): React.Ref<Instance> | null {\n    /**\n     * This will create a new function if the ref props change and are defined.\n     * This means react will call the old forkRef with `null` and the new forkRef\n     * with the ref. Cleanup naturally emerges from this behavior.\n     */\n    return React.useMemo(() => {\n        if (refA == null && refB == null) {\n            return null;\n        }\n        return (refValue) => {\n            setRef(refA, refValue);\n            setRef(refB, refValue);\n        };\n    }, [refA, refB]);\n}\n", "import { TransitionDuration } from '../types';\n\ninterface ComponentProps {\n    style?: React.CSSProperties | undefined;\n    /**\n     * number: 400\n     * TransitionDuration: { enter: 200, exit: 400 }\n     */\n    timeout: number | TransitionDuration;\n    mode: 'enter' | 'exit';\n}\n\ninterface TransitionPropsReturnType {\n    duration: number;\n    easing: string | undefined;\n    delay: string | undefined;\n}\n\nexport default function getTransitionProps(props: ComponentProps): TransitionPropsReturnType {\n    const { timeout, style = {}, mode } = props;\n    return {\n        duration: typeof timeout === 'object' ? timeout[mode] || 0 : timeout,\n        easing: style.transitionTimingFunction,\n        delay: style.transitionDelay,\n    };\n}\n", "/**\n * Credit to MUI team @ https://mui.com\n */\nexport const defaultEasing = {\n    // This is the most common easing curve.\n    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',\n    // Objects enter the screen at full velocity from off-screen and\n    // slowly decelerate to a resting point.\n    easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',\n    // Objects leave the screen at full velocity. They do not decelerate when off-screen.\n    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',\n    // The sharp curve is used by objects that may return to the screen at any time.\n    sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',\n};\n\n/**\n * CSS hack to force a repaint\n */\nexport const reflow = (node: Element): void => {\n    // We have to do something with node.scrollTop.\n    // Otherwise it's removed from the compiled code by optimisers\n    // eslint-disable-next-line no-self-assign\n    node.scrollTop = node.scrollTop;\n};\n", "import { defaultEasing } from './utils';\n\ninterface CreateTransitionOptions {\n    duration: string | number;\n    easing?: string;\n    delay?: string | number;\n}\n\nconst formatMs = (milliseconds: number) => `${Math.round(milliseconds)}ms`;\n\nexport default function createTransition(\n    props: string | string[] = ['all'],\n    options?: CreateTransitionOptions\n): string {\n    const { duration = 300, easing = defaultEasing.easeInOut, delay = 0 } = options || {};\n\n    const properties = Array.isArray(props) ? props : [props];\n\n    return properties\n        .map((animatedProp) => {\n            const formattedDuration = typeof duration === 'string' ? duration : formatMs(duration);\n            const formattedDelay = typeof delay === 'string' ? delay : formatMs(delay);\n            return `${animatedProp} ${formattedDuration} ${easing} ${formattedDelay}`;\n        })\n        .join(',');\n}\n", "/**\n * Credit to MUI team @ https://mui.com\n */\nimport * as React from 'react';\nimport TransitionComponent from '../Transition';\nimport useForkRef from '../useForkRef';\nimport getTransitionProps from '../getTransitionProps';\nimport createTransition from '../createTransition';\nimport { defaultEasing, reflow } from '../utils';\nimport { SlideTransitionDirection, TransitionProps } from '../../types';\n\nfunction ownerDocument(node: Node | null | undefined): Document {\n    return (node && node.ownerDocument) || document;\n}\n\nfunction ownerWindow(node: Node | null): Window {\n    const doc = ownerDocument(node);\n    return doc.defaultView || window;\n}\n\n/**\n * Corresponds to 10 frames at 60 Hz.\n * A few bytes payload overhead when lodash/debounce is ~3 kB and debounce ~300 B.\n */\nfunction debounce(func: () => void, wait = 166) {\n    let timeout: ReturnType<typeof setTimeout>;\n    function debounced(...args: any[]) {\n        const later = () => {\n            // @ts-ignore\n            func.apply(this, args);\n        };\n        clearTimeout(timeout);\n        timeout = setTimeout(later, wait);\n    }\n\n    debounced.clear = () => {\n        clearTimeout(timeout);\n    };\n\n    return debounced;\n}\n\n/**\n * Translate the node so it can't be seen on the screen.\n * Later, we're going to translate the node back to its original location with `none`.\n */\nfunction getTranslateValue(\n    direction: SlideTransitionDirection,\n    node: HTMLElement & { fakeTransform?: string }\n): string {\n    const rect = node.getBoundingClientRect();\n    const containerWindow = ownerWindow(node);\n    let transform;\n\n    if (node.fakeTransform) {\n        transform = node.fakeTransform;\n    } else {\n        const computedStyle = containerWindow.getComputedStyle(node);\n        transform = computedStyle.getPropertyValue('-webkit-transform') || computedStyle.getPropertyValue('transform');\n    }\n\n    let offsetX = 0;\n    let offsetY = 0;\n\n    if (transform && transform !== 'none' && typeof transform === 'string') {\n        const transformValues = transform.split('(')[1].split(')')[0].split(',');\n        offsetX = parseInt(transformValues[4], 10);\n        offsetY = parseInt(transformValues[5], 10);\n    }\n\n    switch (direction) {\n        case 'left':\n            return `translateX(${containerWindow.innerWidth + offsetX - rect.left}px)`;\n        case 'right':\n            return `translateX(-${rect.left + rect.width - offsetX}px)`;\n        case 'up':\n            return `translateY(${containerWindow.innerHeight + offsetY - rect.top}px)`;\n        default:\n            // down\n            return `translateY(-${rect.top + rect.height - offsetY}px)`;\n    }\n}\n\nfunction setTranslateValue(direction: SlideTransitionDirection, node: HTMLElement | null): void {\n    if (!node) return;\n    const transform = getTranslateValue(direction, node);\n    if (transform) {\n        node.style.webkitTransform = transform;\n        node.style.transform = transform;\n    }\n}\n\nconst Slide = React.forwardRef<unknown, TransitionProps>((props, ref) => {\n    const {\n        children,\n        direction = 'down',\n        in: inProp,\n        style,\n        timeout = 0,\n        onEnter,\n        onEntered,\n        onExit,\n        onExited,\n        ...other\n    } = props;\n\n    const nodeRef = React.useRef(null);\n    const handleRefIntermediary = useForkRef((children as any).ref, nodeRef);\n    const handleRef = useForkRef(handleRefIntermediary, ref);\n\n    const handleEnter: TransitionProps['onEnter'] = (node, isAppearing) => {\n        setTranslateValue(direction, node);\n        reflow(node);\n\n        if (onEnter) {\n            onEnter(node, isAppearing);\n        }\n    };\n\n    const handleEntering = (node: HTMLElement) => {\n        const easing = style?.transitionTimingFunction || defaultEasing.easeOut;\n        const transitionProps = getTransitionProps({\n            timeout,\n            mode: 'enter',\n            style: { ...style, transitionTimingFunction: easing },\n        });\n\n        node.style.webkitTransition = createTransition('-webkit-transform', transitionProps);\n        node.style.transition = createTransition('transform', transitionProps);\n\n        node.style.webkitTransform = 'none';\n        node.style.transform = 'none';\n    };\n\n    const handleExit: TransitionProps['onExit'] = (node) => {\n        const easing = style?.transitionTimingFunction || defaultEasing.sharp;\n        const transitionProps = getTransitionProps({\n            timeout,\n            mode: 'exit',\n            style: { ...style, transitionTimingFunction: easing },\n        });\n\n        node.style.webkitTransition = createTransition('-webkit-transform', transitionProps);\n        node.style.transition = createTransition('transform', transitionProps);\n\n        setTranslateValue(direction, node);\n\n        if (onExit) {\n            onExit(node);\n        }\n    };\n\n    const handleExited: TransitionProps['onExited'] = (node) => {\n        // No need for transitions when the component is hidden\n        node.style.webkitTransition = '';\n        node.style.transition = '';\n\n        if (onExited) {\n            onExited(node);\n        }\n    };\n\n    const updatePosition = React.useCallback(() => {\n        if (nodeRef.current) {\n            setTranslateValue(direction, nodeRef.current);\n        }\n    }, [direction]);\n\n    React.useEffect(() => {\n        // Skip configuration where the position is screen size invariant.\n        if (inProp || direction === 'down' || direction === 'right') {\n            return undefined;\n        }\n\n        const handleResize = debounce(() => {\n            if (nodeRef.current) {\n                setTranslateValue(direction, nodeRef.current);\n            }\n        });\n\n        const containerWindow = ownerWindow(nodeRef.current);\n        containerWindow.addEventListener('resize', handleResize);\n        return () => {\n            handleResize.clear();\n            containerWindow.removeEventListener('resize', handleResize);\n        };\n    }, [direction, inProp]);\n\n    React.useEffect(() => {\n        if (!inProp) {\n            // We need to update the position of the drawer when the direction change and\n            // when it's hidden.\n            updatePosition();\n        }\n    }, [inProp, updatePosition]);\n\n    return (\n        <TransitionComponent\n            appear\n            nodeRef={nodeRef}\n            onEnter={handleEnter}\n            onEntered={onEntered}\n            onEntering={handleEntering}\n            onExit={handleExit}\n            onExited={handleExited}\n            in={inProp}\n            timeout={timeout}\n            {...other}\n        >\n            {(state, childProps) =>\n                React.cloneElement(children as any, {\n                    ref: handleRef,\n                    style: {\n                        visibility: state === 'exited' && !inProp ? 'hidden' : undefined,\n                        ...style,\n                        ...(children as any).props.style,\n                    },\n                    ...childProps,\n                })\n            }\n        </TransitionComponent>\n    );\n});\n\nSlide.displayName = 'Slide';\n\nexport default Slide;\n", "import React from 'react';\n\nconst SvgIcon = (props: { children: JSX.Element }) => (\n    <svg\n        viewBox=\"0 0 24 24\"\n        focusable=\"false\"\n        style={{\n            fontSize: 20,\n            marginInlineEnd: 8,\n            userSelect: 'none',\n            width: '1em',\n            height: '1em',\n            display: 'inline-block',\n            fill: 'currentColor',\n            flexShrink: 0,\n        }}\n        {...props}\n    />\n);\n\nconst CheckIcon: React.FC = () => (\n    <SvgIcon>\n        <path\n            d=\"M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41\n        10.59L10 14.17L17.59 6.58L19 8L10 17Z\"\n        />\n    </SvgIcon>\n);\n\nconst WarningIcon: React.FC = () => (\n    <SvgIcon>\n        <path d=\"M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z\" />\n    </SvgIcon>\n);\n\nconst ErrorIcon: React.FC = () => (\n    <SvgIcon>\n        <path\n            d=\"M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,\n        6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,\n        13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z\"\n        />\n    </SvgIcon>\n);\n\nconst InfoIcon: React.FC = () => (\n    <SvgIcon>\n        <path\n            d=\"M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,\n        0 22,12A10,10 0 0,0 12,2Z\"\n        />\n    </SvgIcon>\n);\n\nconst defaultIconVariants: Record<string, React.ReactNode> = {\n    default: undefined,\n    success: <CheckIcon />,\n    warning: <WarningIcon />,\n    error: <ErrorIcon />,\n    info: <InfoIcon />,\n};\n\nexport default defaultIconVariants;\n", "import Slide from '../transitions/Slide';\nimport defaultIconVariants from '../utils/defaultIconVariants';\nimport { InternalSnack } from '../types';\n\nexport const defaults = {\n    maxSnack: 3,\n    persist: false,\n    hideIconVariant: false,\n    disableWindowBlurListener: false,\n    variant: 'default',\n    autoHideDuration: 5000,\n    iconVariant: defaultIconVariants,\n    anchorOrigin: { vertical: 'bottom', horizontal: 'left' },\n    TransitionComponent: Slide,\n    transitionDuration: {\n        enter: 225,\n        exit: 195,\n    },\n};\n\n/**\n * Derives the right autoHideDuration taking into account the following\n * prority order: 1: Options, 2: Props, 3: default fallback\n */\nconst getAutoHideDuration = (optionsDuration: any, propsDuration: any) => {\n    const isNumberOrNull = (numberish: number | null) => typeof numberish === 'number' || numberish === null;\n\n    if (isNumberOrNull(optionsDuration)) return optionsDuration;\n    if (isNumberOrNull(propsDuration)) return propsDuration;\n    return defaults.autoHideDuration;\n};\n\n/**\n * Derives the right transitionDuration taking into account the following\n * prority order: 1: Options, 2: Props, 3: default fallback\n */\nconst getTransitionDuration = (optionsDuration: any, propsDuration: any) => {\n    const is = (item: any, types: string[]) => types.some((t) => typeof item === t);\n\n    if (is(optionsDuration, ['string', 'number'])) {\n        return optionsDuration;\n    }\n\n    if (is(optionsDuration, ['object'])) {\n        return {\n            ...defaults.transitionDuration,\n            ...(is(propsDuration, ['object']) && propsDuration),\n            ...optionsDuration,\n        };\n    }\n\n    if (is(propsDuration, ['string', 'number'])) {\n        return propsDuration;\n    }\n\n    if (is(propsDuration, ['object'])) {\n        return {\n            ...defaults.transitionDuration,\n            ...propsDuration,\n        };\n    }\n\n    return defaults.transitionDuration;\n};\n\nexport const merge =\n    (options: any, props: any) =>\n    (name: keyof InternalSnack, shouldObjectMerge = false): any => {\n        if (shouldObjectMerge) {\n            return {\n                ...(defaults as any)[name],\n                ...props[name],\n                ...options[name],\n            };\n        }\n\n        if (name === 'autoHideDuration') {\n            return getAutoHideDuration(options.autoHideDuration, props.autoHideDuration);\n        }\n\n        if (name === 'transitionDuration') {\n            return getTransitionDuration(options.transitionDuration, props.transitionDuration);\n        }\n\n        return options[name] || props[name] || (defaults as any)[name];\n    };\n", "import { css, CSSAttribute } from 'goober';\n\nexport function makeStyles<S extends { [key: string]: CSSAttribute }, K extends keyof S>(\n    styles: S\n): { [key in K]: string } {\n    return Object.entries(styles).reduce(\n        (acc, [key, value]) => ({\n            ...acc,\n            [key]: css(value),\n        }),\n        {} as { [key in K]: string }\n    );\n}\n\nexport const ComponentClasses = {\n    SnackbarContainer: 'notistack-SnackbarContainer',\n    Snackbar: 'notistack-Snackbar',\n    CollapseWrapper: 'notistack-CollapseWrapper',\n    MuiContent: 'notistack-MuiContent',\n    MuiContentVariant: (variant: string) => `notistack-MuiContent-${variant}`,\n};\n", "/**\n * Credit to MUI team @ https://mui.com\n */\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { reflow } from '../utils';\nimport TransitionComponent from '../Transition';\nimport useForkRef from '../useForkRef';\nimport { TransitionProps } from '../../types';\nimport getTransitionProps from '../getTransitionProps';\nimport createTransition from '../createTransition';\nimport { ComponentClasses, makeStyles } from '../../utils/styles';\n\nconst classes = makeStyles({\n    root: {\n        height: 0,\n    },\n    entered: {\n        height: 'auto',\n    },\n});\n\nconst collapsedSize = '0px';\nconst timeout = 175;\n\ninterface CollapseProps {\n    children: JSX.Element;\n    in: boolean;\n    onExited: TransitionProps['onExited'];\n}\n\nconst Collapse = React.forwardRef<HTMLDivElement, CollapseProps>((props, ref) => {\n    const { children, in: inProp, onExited } = props;\n\n    const wrapperRef = React.useRef<HTMLDivElement>(null);\n\n    const nodeRef = React.useRef<HTMLDivElement>(null);\n    const handleRef = useForkRef(ref, nodeRef);\n\n    const getWrapperSize = () => (wrapperRef.current ? wrapperRef.current.clientHeight : 0);\n\n    const handleEnter: TransitionProps['onEnter'] = (node) => {\n        node.style.height = collapsedSize;\n    };\n\n    const handleEntering = (node: HTMLElement) => {\n        const wrapperSize = getWrapperSize();\n\n        const { duration: transitionDuration, easing } = getTransitionProps({\n            timeout,\n            mode: 'enter',\n        });\n\n        node.style.transitionDuration =\n            typeof transitionDuration === 'string' ? transitionDuration : `${transitionDuration}ms`;\n\n        node.style.height = `${wrapperSize}px`;\n        node.style.transitionTimingFunction = easing || '';\n    };\n\n    const handleEntered: TransitionProps['onEntered'] = (node) => {\n        node.style.height = 'auto';\n    };\n\n    const handleExit: TransitionProps['onExit'] = (node) => {\n        node.style.height = `${getWrapperSize()}px`;\n    };\n\n    const handleExiting = (node: HTMLElement) => {\n        reflow(node);\n\n        const { duration: transitionDuration, easing } = getTransitionProps({\n            timeout,\n            mode: 'exit',\n        });\n\n        node.style.transitionDuration =\n            typeof transitionDuration === 'string' ? transitionDuration : `${transitionDuration}ms`;\n        node.style.height = collapsedSize;\n        node.style.transitionTimingFunction = easing || '';\n    };\n\n    return (\n        <TransitionComponent\n            in={inProp}\n            unmountOnExit\n            onEnter={handleEnter}\n            onEntered={handleEntered}\n            onEntering={handleEntering}\n            onExit={handleExit}\n            onExited={onExited}\n            onExiting={handleExiting}\n            nodeRef={nodeRef}\n            timeout={timeout}\n        >\n            {(state, childProps) => (\n                <div\n                    ref={handleRef}\n                    className={clsx(classes.root, { [classes.entered]: state === 'entered' })}\n                    style={{\n                        pointerEvents: 'all',\n                        overflow: 'hidden',\n                        minHeight: collapsedSize,\n                        transition: createTransition('height'),\n                        ...(state === 'entered' && {\n                            overflow: 'visible',\n                        }),\n                        ...(state === 'exited' &&\n                            !inProp && {\n                                visibility: 'hidden',\n                            }),\n                    }}\n                    {...childProps}\n                >\n                    <div\n                        ref={wrapperRef}\n                        className={ComponentClasses.CollapseWrapper}\n                        // Hack to get children with a negative margin to not falsify the height computation.\n                        style={{ display: 'flex', width: '100%' }}\n                    >\n                        {children}\n                    </div>\n                </div>\n            )}\n        </TransitionComponent>\n    );\n});\n\nCollapse.displayName = 'Collapse';\n\nexport default Collapse;\n", "import {\n    InternalSnack,\n    SlideTransitionDirection,\n    SnackbarOrigin,\n    SnackbarClassKey,\n    SnackbarProviderProps,\n    ClassNameMap,\n    ContainerClassKey,\n} from '../types';\nimport { originKeyExtractor } from '../utils';\n\nconst direction: Record<string, SlideTransitionDirection> = {\n    right: 'left',\n    left: 'right',\n    bottom: 'up',\n    top: 'down',\n};\n\nexport const getSlideDirection = (anchorOrigin: InternalSnack['anchorOrigin']): SlideTransitionDirection => {\n    if (anchorOrigin.horizontal !== 'center') {\n        return direction[anchorOrigin.horizontal];\n    }\n    return direction[anchorOrigin.vertical];\n};\n\n/** Tranforms classes name */\nexport const toSnackbarAnchorOrigin = (anchorOrigin: SnackbarOrigin): SnackbarClassKey =>\n    `anchorOrigin${originKeyExtractor(anchorOrigin)}` as SnackbarClassKey;\n\n/**\n * Omit SnackbarContainer class keys that are not needed for SnackbarItem\n */\nexport const keepSnackbarClassKeys = (\n    classes: SnackbarProviderProps['classes'] = {}\n): Partial<ClassNameMap<SnackbarClassKey>> => {\n    const containerClasses: Record<ContainerClassKey, true> = {\n        containerRoot: true,\n        containerAnchorOriginTopCenter: true,\n        containerAnchorOriginBottomCenter: true,\n        containerAnchorOriginTopRight: true,\n        containerAnchorOriginBottomRight: true,\n        containerAnchorOriginTopLeft: true,\n        containerAnchorOriginBottomLeft: true,\n    };\n    return (Object.keys(classes) as ContainerClassKey[])\n        .filter((key) => !containerClasses[key])\n        .reduce((obj, key) => ({ ...obj, [key]: classes[key] }), {});\n};\n", "import { Snackbar<PERSON>ey } from 'src/types';\n\nconst noOp = () => {\n    /* */\n};\n\n/**\n * Credit to MUI team @ https://mui.com\n * Safe chained function.\n *\n * Will only create a new function if needed,\n * otherwise will pass back existing functions or null.\n */\nexport default function createChainedFunction<Args extends any[], This>(\n    funcs: Array<((this: This, ...args: Args) => any) | undefined>,\n    snackbarId?: SnackbarKey\n): (this: This, ...args: Args) => void {\n    // @ts-ignore\n    return funcs.reduce((acc, func) => {\n        if (func === null || func === undefined) {\n            return acc;\n        }\n\n        return function chainedFunction(...args) {\n            const argums = [...args] as any;\n            if (snackbarId && argums.indexOf(snackbarId) === -1) {\n                argums.push(snackbarId);\n            }\n            // @ts-ignore\n            acc.apply(this, argums);\n            func.apply(this, argums);\n        };\n    }, noOp);\n}\n", "/**\n * Credit to MUI team @ https://mui.com\n * https://github.com/facebook/react/issues/14099#issuecomment-440013892\n */\nimport * as React from 'react';\n\nconst useEnhancedEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n\nexport default function useEventCallback<Args extends unknown[], Return>(\n    fn: (...args: Args) => Return\n): (...args: Args) => Return {\n    const ref = React.useRef(fn);\n    useEnhancedEffect(() => {\n        ref.current = fn;\n    });\n    return React.useCallback(\n        (...args: Args) =>\n            // @ts-expect-error hide `this`\n            (0, ref.current)(...args),\n        []\n    );\n}\n", "/**\n * Credit to MUI team @ https://mui.com\n */\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport useEventCallback from '../utils/useEventCallback';\nimport { CloseReason, SharedProps, SnackbarKey } from '../types';\nimport { ComponentClasses } from '../utils/styles';\n\ninterface SnackbarProps extends Required<Pick<SharedProps, 'disableWindowBlurListener' | 'onClose'>> {\n    open: boolean;\n    id: SnackbarKey;\n    className: string;\n    children: JSX.Element;\n    autoHideDuration: number | null | undefined;\n    SnackbarProps: SharedProps['SnackbarProps'];\n}\n\nconst Snackbar = React.forwardRef<HTMLDivElement, SnackbarProps>((props, ref) => {\n    const {\n        children,\n        className,\n        autoHideDuration,\n        disableWindowBlurListener = false,\n        onClose,\n        id,\n        open,\n        SnackbarProps = {},\n    } = props;\n\n    const timerAutoHide = React.useRef<ReturnType<typeof setTimeout>>();\n\n    const handleClose = useEventCallback((...args: [null, CloseReason, SnackbarKey]) => {\n        if (onClose) {\n            onClose(...args);\n        }\n    });\n\n    const setAutoHideTimer = useEventCallback((autoHideDurationParam?: number | null) => {\n        if (!onClose || autoHideDurationParam == null) {\n            return;\n        }\n\n        if (timerAutoHide.current) {\n            clearTimeout(timerAutoHide.current);\n        }\n        timerAutoHide.current = setTimeout(() => {\n            handleClose(null, 'timeout', id);\n        }, autoHideDurationParam);\n    });\n\n    React.useEffect(() => {\n        if (open) {\n            setAutoHideTimer(autoHideDuration);\n        }\n\n        return () => {\n            if (timerAutoHide.current) {\n                clearTimeout(timerAutoHide.current);\n            }\n        };\n    }, [open, autoHideDuration, setAutoHideTimer]);\n\n    /**\n     * Pause the timer when the user is interacting with the Snackbar\n     * or when the user hide the window.\n     */\n    const handlePause = () => {\n        if (timerAutoHide.current) {\n            clearTimeout(timerAutoHide.current);\n        }\n    };\n\n    /**\n     * Restart the timer when the user is no longer interacting with the Snackbar\n     * or when the window is shown back.\n     */\n    const handleResume = React.useCallback(() => {\n        if (autoHideDuration != null) {\n            setAutoHideTimer(autoHideDuration * 0.5);\n        }\n    }, [autoHideDuration, setAutoHideTimer]);\n\n    const handleMouseEnter: React.MouseEventHandler<HTMLDivElement> = (event) => {\n        if (SnackbarProps.onMouseEnter) {\n            SnackbarProps.onMouseEnter(event);\n        }\n        handlePause();\n    };\n\n    const handleMouseLeave: React.MouseEventHandler<HTMLDivElement> = (event) => {\n        if (SnackbarProps.onMouseLeave) {\n            SnackbarProps.onMouseLeave(event);\n        }\n        handleResume();\n    };\n\n    React.useEffect(() => {\n        if (!disableWindowBlurListener && open) {\n            window.addEventListener('focus', handleResume);\n            window.addEventListener('blur', handlePause);\n\n            return () => {\n                window.removeEventListener('focus', handleResume);\n                window.removeEventListener('blur', handlePause);\n            };\n        }\n\n        return undefined;\n    }, [disableWindowBlurListener, handleResume, open]);\n\n    return (\n        <div\n            ref={ref}\n            {...SnackbarProps}\n            className={clsx(ComponentClasses.Snackbar, className)}\n            onMouseEnter={handleMouseEnter}\n            onMouseLeave={handleMouseLeave}\n        >\n            {children}\n        </div>\n    );\n});\n\nSnackbar.displayName = 'Snackbar';\n\nexport default Snackbar;\n", "import React, { forwardRef } from 'react';\nimport clsx from 'clsx';\nimport { SnackbarContentProps } from '../types';\nimport { breakpoints } from '../utils';\nimport { makeStyles } from '../utils/styles';\n\nconst classes = makeStyles({\n    root: {\n        display: 'flex',\n        flexWrap: 'wrap',\n        flexGrow: 1,\n        [breakpoints.upSm]: {\n            flexGrow: 'initial',\n            minWidth: '288px',\n        },\n    },\n});\n\nconst SnackbarContent = forwardRef<HTMLDivElement, SnackbarContentProps>(({ className, ...props }, ref) => (\n    <div ref={ref} className={clsx(classes.root, className)} {...props} />\n));\n\nSnackbarContent.displayName = 'SnackbarContent';\n\nexport default SnackbarContent;\n", "import React, { memo, forwardRef } from 'react';\nimport clsx from 'clsx';\nimport SnackbarContent from '../../SnackbarContent';\nimport { CustomContentProps } from '../../types';\nimport { ComponentClasses, makeStyles } from '../../utils/styles';\n\nconst classes = makeStyles({\n    root: {\n        backgroundColor: '#313131', // dark grey\n        fontSize: '0.875rem',\n        lineHeight: 1.43,\n        letterSpacing: '0.01071em',\n        color: '#fff',\n        alignItems: 'center',\n        padding: '6px 16px',\n        borderRadius: '4px',\n        boxShadow:\n            '0px 3px 5px -1px rgba(0,0,0,0.2),0px 6px 10px 0px rgba(0,0,0,0.14),0px 1px 18px 0px rgba(0,0,0,0.12)',\n    },\n    lessPadding: {\n        paddingLeft: `${8 * 2.5}px`,\n    },\n    default: {\n        backgroundColor: '#313131', // dark grey\n    },\n    success: {\n        backgroundColor: '#43a047', // green\n    },\n    error: {\n        backgroundColor: '#d32f2f', // dark red\n    },\n    warning: {\n        backgroundColor: '#ff9800', // amber\n    },\n    info: {\n        backgroundColor: '#2196f3', // nice blue\n    },\n    message: {\n        display: 'flex',\n        alignItems: 'center',\n        padding: '8px 0',\n    },\n    action: {\n        display: 'flex',\n        alignItems: 'center',\n        marginLeft: 'auto',\n        paddingLeft: '16px',\n        marginRight: '-8px',\n    },\n});\n\nconst ariaDescribedby = 'notistack-snackbar';\n\nconst MaterialDesignContent = forwardRef<HTMLDivElement, CustomContentProps>((props, forwardedRef) => {\n    const {\n        id,\n        message,\n        action: componentOrFunctionAction,\n        iconVariant,\n        variant,\n        hideIconVariant,\n        style,\n        className,\n    } = props;\n\n    const icon = iconVariant[variant];\n\n    let action = componentOrFunctionAction;\n    if (typeof action === 'function') {\n        action = action(id);\n    }\n\n    return (\n        <SnackbarContent\n            ref={forwardedRef}\n            role=\"alert\"\n            aria-describedby={ariaDescribedby}\n            style={style}\n            className={clsx(\n                ComponentClasses.MuiContent,\n                ComponentClasses.MuiContentVariant(variant),\n                classes.root,\n                { [classes.lessPadding]: !hideIconVariant && icon },\n                classes[variant],\n                className\n            )}\n        >\n            <div id={ariaDescribedby} className={classes.message}>\n                {!hideIconVariant ? icon : null}\n                {message}\n            </div>\n            {action && <div className={classes.action}>{action}</div>}\n        </SnackbarContent>\n    );\n});\n\nMaterialDesignContent.displayName = 'MaterialDesignContent';\n\nexport default memo(MaterialDesignContent);\n", "import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';\nimport clsx from 'clsx';\nimport Collapse from '../transitions/Collapse';\nimport { getSlideDirection, toSnackbarAnchorOrigin, keepSnackbarClassKeys } from './utils';\nimport {\n    TransitionHandlerProps,\n    SnackbarProviderProps,\n    CustomContentProps,\n    InternalSnack,\n    SharedProps,\n} from '../types';\nimport createChainedFunction from '../utils/createChainedFunction';\nimport Snackbar from './Snackbar';\nimport { makeStyles } from '../utils/styles';\nimport MaterialDesignContent from '../ui/MaterialDesignContent';\n\nconst styles = makeStyles({\n    wrappedRoot: {\n        width: '100%',\n        position: 'relative',\n        transform: 'translateX(0)',\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        minWidth: '288px',\n    },\n});\n\ninterface SnackbarItemProps extends Required<Pick<SnackbarProviderProps, 'onEntered' | 'onExited' | 'onClose'>> {\n    snack: InternalSnack;\n    classes: SnackbarProviderProps['classes'];\n    onEnter: SnackbarProviderProps['onEnter'];\n    onExit: SnackbarProviderProps['onExit'];\n    Component?: React.ComponentType<CustomContentProps>;\n}\n\nconst SnackbarItem: React.FC<SnackbarItemProps> = (props) => {\n    const timeout = useRef<ReturnType<typeof setTimeout>>();\n    const [collapsed, setCollapsed] = useState(true);\n\n    const handleClose: NonNullable<SharedProps['onClose']> = createChainedFunction([\n        props.snack.onClose,\n        props.onClose,\n    ]);\n\n    const handleEntered: TransitionHandlerProps['onEntered'] = () => {\n        if (props.snack.requestClose) {\n            handleClose(null, 'instructed', props.snack.id);\n        }\n    };\n\n    const handleExitedScreen = useCallback((): void => {\n        timeout.current = setTimeout(() => {\n            setCollapsed((col) => !col);\n        }, 125);\n    }, []);\n\n    useEffect(\n        () => (): void => {\n            if (timeout.current) {\n                clearTimeout(timeout.current);\n            }\n        },\n        []\n    );\n\n    const { snack, classes: allClasses, Component = MaterialDesignContent } = props;\n\n    const classes = useMemo(() => keepSnackbarClassKeys(allClasses), [allClasses]);\n\n    const {\n        open,\n        SnackbarProps,\n        TransitionComponent,\n        TransitionProps,\n        transitionDuration,\n        disableWindowBlurListener,\n        content: componentOrFunctionContent,\n        entered: ignoredEntered,\n        requestClose: ignoredRequestClose,\n        onEnter: ignoreOnEnter,\n        onEntered: ignoreOnEntered,\n        onExit: ignoreOnExit,\n        onExited: ignoreOnExited,\n        ...otherSnack\n    } = snack;\n\n    const transitionProps = {\n        direction: getSlideDirection(otherSnack.anchorOrigin),\n        timeout: transitionDuration,\n        ...TransitionProps,\n    };\n\n    let content = componentOrFunctionContent;\n    if (typeof content === 'function') {\n        content = content(otherSnack.id, otherSnack.message);\n    }\n\n    const callbacks: { [key in keyof TransitionHandlerProps]?: any } = (\n        ['onEnter', 'onEntered', 'onExit', 'onExited'] as (keyof TransitionHandlerProps)[]\n    ).reduce(\n        (acc, cbName) => ({\n            ...acc,\n            [cbName]: createChainedFunction([props.snack[cbName] as any, props[cbName] as any], otherSnack.id),\n        }),\n        {}\n    );\n\n    return (\n        <Collapse in={collapsed} onExited={callbacks.onExited}>\n            <Snackbar\n                open={open}\n                id={otherSnack.id}\n                disableWindowBlurListener={disableWindowBlurListener}\n                autoHideDuration={otherSnack.autoHideDuration}\n                className={clsx(\n                    styles.wrappedRoot,\n                    classes.root,\n                    classes[toSnackbarAnchorOrigin(otherSnack.anchorOrigin)]\n                )}\n                SnackbarProps={SnackbarProps}\n                onClose={handleClose}\n            >\n                <TransitionComponent\n                    {...transitionProps}\n                    appear\n                    in={open}\n                    onExit={callbacks.onExit}\n                    onExited={handleExitedScreen}\n                    onEnter={callbacks.onEnter}\n                    // order matters. first callbacks.onEntered to set entered: true,\n                    // then handleEntered to check if there's a request for closing\n                    onEntered={createChainedFunction([callbacks.onEntered, handleEntered], otherSnack.id)}\n                >\n                    {(content as React.ReactElement) || <Component {...otherSnack} />}\n                </TransitionComponent>\n            </Snackbar>\n        </Collapse>\n    );\n};\n\nexport default SnackbarItem;\n", "import React, { memo } from 'react';\nimport clsx from 'clsx';\nimport createTransition from '../transitions/createTransition';\nimport { makeStyles, ComponentClasses } from '../utils/styles';\nimport { breakpoints, originKeyExtractor } from '../utils';\nimport { ContainerClassKey, SnackbarProviderProps } from '../types';\n\nconst indents = {\n    view: { default: 20, dense: 4 },\n    snackbar: { default: 6, dense: 2 },\n};\n\nconst collapseWrapper = `.${ComponentClasses.CollapseWrapper}`;\n\nconst xsWidthMargin = 16;\n\nconst styles = makeStyles({\n    root: {\n        boxSizing: 'border-box',\n        display: 'flex',\n        maxHeight: '100%',\n        position: 'fixed',\n        zIndex: 1400,\n        height: 'auto',\n        width: 'auto',\n        transition: createTransition(['top', 'right', 'bottom', 'left', 'max-width'], {\n            duration: 300,\n            easing: 'ease',\n        }),\n        // container itself is invisible and should not block clicks, clicks should be passed to its children\n        // a pointerEvents: all is applied in the collapse component\n        pointerEvents: 'none',\n        [collapseWrapper]: {\n            padding: `${indents.snackbar.default}px 0px`,\n            transition: 'padding 300ms ease 0ms',\n        },\n        maxWidth: `calc(100% - ${indents.view.default * 2}px)`,\n        [breakpoints.downXs]: {\n            width: '100%',\n            maxWidth: `calc(100% - ${xsWidthMargin * 2}px)`,\n        },\n    },\n    rootDense: {\n        [collapseWrapper]: {\n            padding: `${indents.snackbar.dense}px 0px`,\n        },\n    },\n    top: {\n        top: `${indents.view.default - indents.snackbar.default}px`,\n        flexDirection: 'column',\n    },\n    bottom: {\n        bottom: `${indents.view.default - indents.snackbar.default}px`,\n        flexDirection: 'column-reverse',\n    },\n    left: {\n        left: `${indents.view.default}px`,\n        [breakpoints.upSm]: {\n            alignItems: 'flex-start',\n        },\n        [breakpoints.downXs]: {\n            left: `${xsWidthMargin}px`,\n        },\n    },\n    right: {\n        right: `${indents.view.default}px`,\n        [breakpoints.upSm]: {\n            alignItems: 'flex-end',\n        },\n        [breakpoints.downXs]: {\n            right: `${xsWidthMargin}px`,\n        },\n    },\n    center: {\n        left: '50%',\n        transform: 'translateX(-50%)',\n        [breakpoints.upSm]: {\n            alignItems: 'center',\n        },\n    },\n});\n\ninterface SnackbarContainerProps {\n    children: React.ReactNode;\n    dense: SnackbarProviderProps['dense'];\n    anchorOrigin: NonNullable<SnackbarProviderProps['anchorOrigin']>;\n    classes: SnackbarProviderProps['classes'];\n}\n\nconst SnackbarContainer: React.FC<SnackbarContainerProps> = (props) => {\n    const { classes = {}, anchorOrigin, dense, children } = props;\n\n    const combinedClassname = clsx(\n        ComponentClasses.SnackbarContainer,\n        styles[anchorOrigin.vertical],\n        styles[anchorOrigin.horizontal],\n        { [styles.rootDense]: dense },\n        styles.root, // root should come after others to override maxWidth\n        classes.containerRoot,\n        classes[`containerAnchorOrigin${originKeyExtractor(anchorOrigin)}` as ContainerClassKey]\n    );\n\n    return <div className={combinedClassname}>{children}</div>;\n};\n\nexport default memo(SnackbarContainer);\n", "import React, { Component, isValidElement } from 'react';\nimport { createPortal } from 'react-dom';\nimport clsx from 'clsx';\nimport SnackbarContext from '../SnackbarContext';\nimport { originKeyExtractor, isDefined } from '../utils';\nimport { defaults, merge } from './merger';\nimport SnackbarItem from '../SnackbarItem';\nimport SnackbarContainer from '../SnackbarContainer';\nimport warning from '../utils/warning';\nimport {\n    SnackbarProviderProps,\n    SnackbarKey,\n    ProviderContext,\n    TransitionHandlerProps,\n    InternalSnack,\n    OptionsObject,\n    SharedProps,\n    SnackbarMessage,\n} from '../types';\nimport createChainedFunction from '../utils/createChainedFunction';\n\nconst isOptions = (\n    messageOrOptions: SnackbarMessage | (OptionsObject & { message?: SnackbarMessage })\n): messageOrOptions is OptionsObject & { message?: SnackbarMessage } => {\n    const isMessage = typeof messageOrOptions === 'string' || isValidElement(messageOrOptions);\n    return !isMessage;\n};\n\ntype Reducer = (state: State) => State;\ntype SnacksByPosition = { [key: string]: InternalSnack[] };\n\ninterface State {\n    snacks: InternalSnack[];\n    queue: InternalSnack[];\n    contextValue: ProviderContext;\n}\n\nexport let enqueueSnackbar: ProviderContext['enqueueSnackbar'];\nexport let closeSnackbar: ProviderContext['closeSnackbar'];\n\nclass SnackbarProvider extends Component<SnackbarProviderProps, State> {\n    constructor(props: SnackbarProviderProps) {\n        super(props);\n        enqueueSnackbar = this.enqueueSnackbar;\n        closeSnackbar = this.closeSnackbar;\n\n        this.state = {\n            snacks: [],\n            queue: [],\n            contextValue: {\n                enqueueSnackbar: this.enqueueSnackbar.bind(this),\n                closeSnackbar: this.closeSnackbar.bind(this),\n            },\n        };\n    }\n\n    get maxSnack(): number {\n        return this.props.maxSnack || defaults.maxSnack;\n    }\n\n    /**\n     * Adds a new snackbar to the queue to be presented.\n     * Returns generated or user defined key referencing the new snackbar or null\n     */\n    enqueueSnackbar = (\n        messageOrOptions: SnackbarMessage | (OptionsObject & { message?: SnackbarMessage }),\n        optsOrUndefined: OptionsObject = {}\n    ): SnackbarKey => {\n        if (messageOrOptions === undefined || messageOrOptions === null) {\n            throw new Error('enqueueSnackbar called with invalid argument');\n        }\n\n        const opts = isOptions(messageOrOptions) ? messageOrOptions : optsOrUndefined;\n\n        const message: SnackbarMessage | undefined = isOptions(messageOrOptions)\n            ? messageOrOptions.message\n            : messageOrOptions;\n\n        const { key, preventDuplicate, ...options } = opts;\n\n        const hasSpecifiedKey = isDefined(key);\n        const id = hasSpecifiedKey ? (key as SnackbarKey) : new Date().getTime() + Math.random();\n\n        const merger = merge(options, this.props);\n        const snack: InternalSnack = {\n            id,\n            ...options,\n            message,\n            open: true,\n            entered: false,\n            requestClose: false,\n            persist: merger('persist'),\n            action: merger('action'),\n            content: merger('content'),\n            variant: merger('variant'),\n            anchorOrigin: merger('anchorOrigin'),\n            disableWindowBlurListener: merger('disableWindowBlurListener'),\n            autoHideDuration: merger('autoHideDuration'),\n            hideIconVariant: merger('hideIconVariant'),\n            TransitionComponent: merger('TransitionComponent'),\n            transitionDuration: merger('transitionDuration'),\n            TransitionProps: merger('TransitionProps', true),\n            iconVariant: merger('iconVariant', true),\n            style: merger('style', true),\n            SnackbarProps: merger('SnackbarProps', true),\n            className: clsx(this.props.className, options.className),\n        };\n\n        if (snack.persist) {\n            snack.autoHideDuration = undefined;\n        }\n\n        this.setState((state) => {\n            if ((preventDuplicate === undefined && this.props.preventDuplicate) || preventDuplicate) {\n                const compareFunction = (item: InternalSnack): boolean =>\n                    hasSpecifiedKey ? item.id === id : item.message === message;\n\n                const inQueue = state.queue.findIndex(compareFunction) > -1;\n                const inView = state.snacks.findIndex(compareFunction) > -1;\n                if (inQueue || inView) {\n                    return state;\n                }\n            }\n\n            return this.handleDisplaySnack({\n                ...state,\n                queue: [...state.queue, snack],\n            });\n        });\n\n        return id;\n    };\n\n    /**\n     * Reducer: Display snack if there's space for it. Otherwise, immediately\n     * begin dismissing the oldest message to start showing the new one.\n     */\n    handleDisplaySnack: Reducer = (state) => {\n        const { snacks } = state;\n        if (snacks.length >= this.maxSnack) {\n            return this.handleDismissOldest(state);\n        }\n        return this.processQueue(state);\n    };\n\n    /**\n     * Reducer: Display items (notifications) in the queue if there's space for them.\n     */\n    processQueue: Reducer = (state) => {\n        const { queue, snacks } = state;\n        if (queue.length > 0) {\n            return {\n                ...state,\n                snacks: [...snacks, queue[0]],\n                queue: queue.slice(1, queue.length),\n            };\n        }\n        return state;\n    };\n\n    /**\n     * Reducer: Hide oldest snackbar on the screen because there exists a new one which we have to display.\n     * (ignoring the one with 'persist' flag. i.e. explicitly told by user not to get dismissed).\n     *\n     * Note 1: If there is already a message leaving the screen, no new messages are dismissed.\n     * Note 2: If the oldest message has not yet entered the screen, only a request to close the\n     *         snackbar is made. Once it entered the screen, it will be immediately dismissed.\n     */\n    handleDismissOldest: Reducer = (state) => {\n        if (state.snacks.some((item) => !item.open || item.requestClose)) {\n            return state;\n        }\n\n        let popped = false;\n        let ignore = false;\n\n        const persistentCount = state.snacks.reduce(\n            (acc, current) => acc + (current.open && current.persist ? 1 : 0),\n            0\n        );\n\n        if (persistentCount === this.maxSnack) {\n            warning('NO_PERSIST_ALL');\n            ignore = true;\n        }\n\n        const snacks = state.snacks.map((item) => {\n            if (!popped && (!item.persist || ignore)) {\n                popped = true;\n\n                if (!item.entered) {\n                    return {\n                        ...item,\n                        requestClose: true,\n                    };\n                }\n\n                if (item.onClose) {\n                    item.onClose(null, 'maxsnack', item.id);\n                }\n\n                if (this.props.onClose) {\n                    this.props.onClose(null, 'maxsnack', item.id);\n                }\n\n                return {\n                    ...item,\n                    open: false,\n                };\n            }\n\n            return { ...item };\n        });\n\n        return { ...state, snacks };\n    };\n\n    /**\n     * Set the entered state of the snackbar with the given key.\n     */\n    handleEnteredSnack: TransitionHandlerProps['onEntered'] = (node, isAppearing, key) => {\n        if (!isDefined(key)) {\n            throw new Error('handleEnteredSnack Cannot be called with undefined key');\n        }\n\n        this.setState(({ snacks }) => ({\n            snacks: snacks.map((item) => (item.id === key ? { ...item, entered: true } : { ...item })),\n        }));\n    };\n\n    /**\n     * Hide a snackbar after its timeout.\n     */\n    handleCloseSnack: NonNullable<SharedProps['onClose']> = (event, reason, key) => {\n        // should not use createChainedFunction for onClose.\n        // because this.closeSnackbar called this function\n        if (this.props.onClose) {\n            this.props.onClose(event, reason, key);\n        }\n\n        const shouldCloseAll = key === undefined;\n\n        this.setState(({ snacks, queue }) => ({\n            snacks: snacks.map((item) => {\n                if (!shouldCloseAll && item.id !== key) {\n                    return { ...item };\n                }\n\n                return item.entered ? { ...item, open: false } : { ...item, requestClose: true };\n            }),\n            queue: queue.filter((item) => item.id !== key),\n        }));\n    };\n\n    /**\n     * Close snackbar with the given key\n     */\n    closeSnackbar: ProviderContext['closeSnackbar'] = (key) => {\n        // call individual snackbar onClose callback passed through options parameter\n        const toBeClosed = this.state.snacks.find((item) => item.id === key);\n        if (isDefined(key) && toBeClosed && toBeClosed.onClose) {\n            toBeClosed.onClose(null, 'instructed', key);\n        }\n\n        this.handleCloseSnack(null, 'instructed', key);\n    };\n\n    /**\n     * When we set open attribute of a snackbar to false (i.e. after we hide a snackbar),\n     * it leaves the screen and immediately after leaving animation is done, this method\n     * gets called. We remove the hidden snackbar from state and then display notifications\n     * waiting in the queue (if any). If after this process the queue is not empty, the\n     * oldest message is dismissed.\n     */\n    handleExitedSnack: TransitionHandlerProps['onExited'] = (node, key) => {\n        if (!isDefined(key)) {\n            throw new Error('handleExitedSnack Cannot be called with undefined key');\n        }\n\n        this.setState((state) => {\n            const newState = this.processQueue({\n                ...state,\n                snacks: state.snacks.filter((item) => item.id !== key),\n            });\n\n            if (newState.queue.length === 0) {\n                return newState;\n            }\n\n            return this.handleDismissOldest(newState);\n        });\n    };\n\n    render(): JSX.Element {\n        const { contextValue } = this.state;\n        const { domRoot, children, dense = false, Components = {}, classes } = this.props;\n\n        const categ = this.state.snacks.reduce<SnacksByPosition>((acc, current) => {\n            const category = originKeyExtractor(current.anchorOrigin);\n            const existingOfCategory = acc[category] || [];\n            return {\n                ...acc,\n                [category]: [...existingOfCategory, current],\n            };\n        }, {});\n\n        const snackbars = Object.keys(categ).map((origin) => {\n            const snacks = categ[origin];\n            const [nomineeSnack] = snacks;\n            return (\n                <SnackbarContainer\n                    key={origin}\n                    dense={dense}\n                    anchorOrigin={nomineeSnack.anchorOrigin}\n                    classes={classes}\n                >\n                    {snacks.map((snack) => (\n                        <SnackbarItem\n                            key={snack.id}\n                            snack={snack}\n                            classes={classes}\n                            Component={Components[snack.variant]}\n                            onClose={this.handleCloseSnack}\n                            onEnter={this.props.onEnter}\n                            onExit={this.props.onExit}\n                            onExited={createChainedFunction([this.handleExitedSnack, this.props.onExited], snack.id)}\n                            onEntered={createChainedFunction([this.handleEnteredSnack, this.props.onEntered], snack.id)}\n                        />\n                    ))}\n                </SnackbarContainer>\n            );\n        });\n\n        return (\n            <SnackbarContext.Provider value={contextValue}>\n                {children}\n                {domRoot ? createPortal(snackbars, domRoot) : snackbars}\n            </SnackbarContext.Provider>\n        );\n    }\n}\n\nexport default SnackbarProvider;\n", "import { useContext } from 'react';\nimport SnackbarContext from './SnackbarContext';\nimport { ProviderContext } from './types';\n\nexport default (): ProviderContext => useContext(SnackbarContext);\n"], "names": ["noOp", "React", "createContext", "enqueueSnackbar", "closeSnackbar", "capitalise", "text", "char<PERSON>t", "toUpperCase", "slice", "originKeyExtractor", "anchor", "vertical", "horizontal", "isDefined", "value", "Transition", "props", "initialStatus", "appear", "appearStatus", "unmountOnExit", "mountOnEnter", "state", "status", "nextCallback", "getDerivedStateFromProps", "prevState", "componentDidMount", "updateStatus", "this", "componentDidUpdate", "prevProps", "nextStatus", "componentWillUnmount", "cancelNextCallback", "getTimeouts", "timeout", "enter", "exit", "mounting", "performEnter", "performExit", "setState", "isAppearing", "timeouts", "onEnter", "node", "safeSetState", "_this2", "onEntering", "onTransitionEnd", "onEntered", "onExit", "_this3", "onExiting", "onExited", "cancel", "nextState", "callback", "setNextCallback", "active", "_this4", "handler", "addEndListener", "setTimeout", "render", "children", "nodeRef", "_this$props$nodeRef", "current", "Error", "Component", "noop", "setRef", "ref", "useForkRef", "refA", "refB", "refValue", "getTransitionProps", "style", "duration", "mode", "easing", "transitionTimingFunction", "delay", "transitionDelay", "defaultProps", "reflow", "scrollTop", "formatMs", "milliseconds", "Math", "round", "createTransition", "options", "Array", "isArray", "map", "animatedProp", "formattedDuration", "formattedDelay", "join", "ownerWindow", "ownerDocument", "document", "defaultView", "window", "setTranslateValue", "direction", "transform", "rect", "getBoundingClientRect", "containerWindow", "fakeTransform", "computedStyle", "getComputedStyle", "getPropertyValue", "offsetX", "offsetY", "transformValues", "split", "parseInt", "innerWidth", "left", "width", "innerHeight", "top", "height", "getTranslateValue", "webkitTransform", "Slide", "inProp", "other", "handleRefIntermediary", "handleRef", "updatePosition", "handleResize", "func", "wait", "debounced", "args", "later", "apply", "_this", "clearTimeout", "clear", "debounce", "addEventListener", "removeEventListener", "TransitionComponent", "transitionProps", "webkitTransition", "transition", "childProps", "visibility", "undefined", "displayName", "SvgIcon", "viewBox", "focusable", "fontSize", "marginInlineEnd", "userSelect", "display", "fill", "flexShrink", "CheckIcon", "d", "WarningIcon", "ErrorIcon", "InfoIcon", "defaults", "maxSnack", "persist", "hideIconVariant", "disableWindowBlurListener", "variant", "autoHideDuration", "icon<PERSON><PERSON><PERSON>", "success", "warning", "error", "info", "anchor<PERSON><PERSON><PERSON>", "transitionDuration", "makeStyles", "styles", "Object", "entries", "reduce", "acc", "css", "ComponentClasses", "classes", "root", "entered", "Collapse", "wrapperRef", "getWrapperSize", "clientHeight", "wrapperSize", "className", "clsx", "pointerEvents", "overflow", "minHeight", "right", "bottom", "toSnackbarAnchorOrigin", "createChainedFunction", "funcs", "snackbarId", "argums", "indexOf", "push", "useEnhancedEffect", "useEventCallback", "fn", "Snackbar", "onClose", "id", "open", "SnackbarProps", "timerAutoHide", "handleClose", "setAutoHideTimer", "autoHideDurationParam", "handlePause", "handleResume", "onMouseEnter", "event", "onMouseLeave", "flexWrap", "flexGrow", "min<PERSON><PERSON><PERSON>", "SnackbarContent", "forwardRef", "backgroundColor", "lineHeight", "letterSpacing", "color", "alignItems", "padding", "borderRadius", "boxShadow", "lessPadding", "paddingLeft", "message", "action", "marginLeft", "marginRight", "MaterialDesignContent", "forwardedRef", "icon", "role", "memo", "wrappedRoot", "position", "SnackbarItem", "useRef", "useState", "collapsed", "setCollapsed", "snack", "handleExitedScreen", "useCallback", "col", "useEffect", "allClasses", "useMemo", "containerClasses", "containerRoot", "containerAnchorOriginTopCenter", "containerAnchorOriginBottomCenter", "containerAnchorOriginTopRight", "containerAnchorOriginBottomRight", "containerAnchorOriginTopLeft", "containerAnchorOriginBottomLeft", "keys", "filter", "key", "obj", "keepSnackbarClassKeys", "TransitionProps", "componentOrFunctionContent", "content", "ignoredEntered", "otherSnack", "callbacks", "cbName", "requestClose", "boxSizing", "maxHeight", "zIndex", "indents", "max<PERSON><PERSON><PERSON>", "rootDense", "flexDirection", "xsWidthMargin", "center", "dense", "combinedClassname", "isOptions", "messageOrOptions", "isValidElement", "SnackbarProvider", "optsOrUndefined", "opts", "preventDuplicate", "hasSpecifiedKey", "Date", "getTime", "random", "merger", "name", "shouldObjectMerge", "propsDuration", "isNumberOrNull", "numberish", "optionsDuration", "is", "item", "types", "some", "t", "getTransitionDuration", "merge", "compareFunction", "inQueue", "queue", "findIndex", "inView", "snacks", "handleDisplaySnack", "length", "handleDismissOldest", "processQueue", "popped", "ignore", "reason", "shouldCloseAll", "toBeClosed", "find", "handleCloseSnack", "newState", "contextValue", "bind", "domRoot", "Components", "categ", "category", "snackbars", "origin", "SnackbarContainer", "handleExitedSnack", "handleEnteredSnack", "SnackbarContext", "Provider", "createPortal", "useContext"], "mappings": "o/BAGA,IAAMA,EAAO,iBACF,MAGIC,EAAMC,cAA+B,CAChDC,gBAAiBH,EACjBI,cAAeJ,ICFbK,EAAa,SAACC,UAAyBA,EAAKC,OAAO,GAAGC,cAAgBF,EAAKG,MAAM,IAE1EC,EAAqB,SAACC,YAC5BN,EAAWM,EAAOC,UAAYP,EAAWM,EAAOE,aAE1CC,EAAY,SAACC,WAAyDA,GAAmB,IAAVA,GCsCtFC,yBAKUC,uBACFA,aAIFC,EAFIC,EAAWF,EAAXE,gBAIHC,aAAe,KAEhBH,KACIE,GACAD,EA9BD,WA+BME,aA9BJ,YAgCDF,EA/BA,UAkCJA,EADOD,EAAMI,eAAiBJ,EAAMK,aApC9B,YACH,WAyCFC,MAAQ,CAAEC,OAAQN,KAElBO,aAAe,gBAGjBC,yBAAP,WAA0EC,gBA/C5D,cAgDIA,EAAUH,OACb,CAAEA,OAhDN,UAkDA,iCAGXI,kBAAA,gBACSC,cAAa,EAAMC,KAAKV,iBAGjCW,mBAAA,SAAmBC,OACXC,EAAsC,QACtCD,IAAcF,KAAKb,MAAO,KAClBO,EAAWM,KAAKP,MAAhBC,OAEJM,KAAKb,SA7DJ,aA8DGO,GA7DJ,YA6D2BA,IACvBS,EA/DH,YAAA,aAiEMT,GAhEP,YAgE8BA,IAC9BS,EAhEA,gBAmEHJ,cAAa,EAAOI,MAG7BC,qBAAA,gBACSC,wBAGTC,YAAA,eACYC,EAAYP,KAAKb,MAAjBoB,QACJC,EAAQD,EACRE,EAAOF,SAEI,MAAXA,GAAsC,iBAAZA,GAA2C,iBAAZA,IACzDE,EAAOF,EAAQE,KACfD,EAAQD,EAAQC,OAEb,CACHC,KAAMA,EACND,MAAOA,MAIfT,aAAA,SAAaW,EAAkBP,YAAlBO,IAAAA,GAAW,GACD,OAAfP,QACKE,qBA7FA,aA+FDF,OACKQ,aAAaD,QAEbE,eAEFZ,KAAKb,MAAMI,eArGf,WAqGgCS,KAAKP,MAAMC,aACzCmB,SAAS,CAAEnB,OAvGV,iBAmHdiB,aAAA,SAAaD,cACDF,EAAUR,KAAKb,MAAfqB,MACFM,EAAcJ,EAEdK,EAAWf,KAAKM,cAEjBI,GAAaF,GASdR,KAAKb,MAAM6B,cACN7B,MAAM6B,QAAQhB,KAAKiB,KAAMH,QAG7BI,aAAa,CAAExB,OApIX,aAoI+B,WAChCyB,EAAKhC,MAAMiC,YACXD,EAAKhC,MAAMiC,WAAWD,EAAKF,KAAMH,GAGrCK,EAAKE,gBAAgBN,EAASP,OAAO,WACjCW,EAAKD,aAAa,CAAExB,OAzIpB,YAyIuC,WAC/ByB,EAAKhC,MAAMmC,WACXH,EAAKhC,MAAMmC,UAAUH,EAAKF,KAAMH,kBApBvCI,aAAa,CAAExB,OAvHhB,YAuHmC,WAC/ByB,EAAKhC,MAAMmC,WACXH,EAAKhC,MAAMmC,UAAUH,EAAKF,KAAMH,SAyBhDF,YAAA,sBACYH,EAAST,KAAKb,MAAdsB,KACFM,EAAWf,KAAKM,cAGjBG,GASDT,KAAKb,MAAMoC,aACNpC,MAAMoC,OAAOvB,KAAKiB,WAGtBC,aAAa,CAAExB,OAnKZ,YAmK+B,WAC/B8B,EAAKrC,MAAMsC,WACXD,EAAKrC,MAAMsC,UAAUD,EAAKP,MAG9BO,EAAKH,gBAAgBN,EAASN,MAAM,WAChCe,EAAKN,aAAa,CAAExB,OA5KrB,WA4KuC,WAC9B8B,EAAKrC,MAAMuC,UACXF,EAAKrC,MAAMuC,SAASF,EAAKP,qBApBhCC,aAAa,CAAExB,OA1JjB,WA0JmC,WAC9B8B,EAAKrC,MAAMuC,UACXF,EAAKrC,MAAMuC,SAASF,EAAKP,YAyBzCZ,mBAAA,WAC8B,OAAtBL,KAAKL,cAAyBK,KAAKL,aAAagC,cAC3ChC,aAAagC,cACbhC,aAAe,SAI5BuB,aAAA,SAAaU,EAAkBC,GAC3BA,EAAW7B,KAAK8B,gBAAgBD,QAC3BhB,SAASe,EAAWC,MAG7BC,gBAAA,SAAgBD,cACRE,GAAS,cAERpC,aAAe,WACZoC,IACAA,GAAS,EACTC,EAAKrC,aAAe,KAEpBkC,WAIFlC,aAA8BgC,OAAS,WACzCI,GAAS,GAGN/B,KAAKL,gBAGhB0B,gBAAA,SAAgBd,EAAiB0B,QACxBH,gBAAgBG,IAEhBjC,KAAKiB,MADsC,MAAXV,IAAoBP,KAAKb,MAAM+C,eAEhEC,WAAWnC,KAAKL,aAA8B,IAI9CK,KAAKb,MAAM+C,qBACN/C,MAAM+C,eAAelC,KAAKiB,KAAMjB,KAAKL,cAG/B,MAAXY,GACA4B,WAAWnC,KAAKL,aAA8BY,OAItD6B,OAAA,eACY1C,EAAWM,KAAKP,MAAhBC,UAvOE,cAyONA,SACO,WAsBPM,KAAKb,aAEFkD,IApBHA,UAoBY3C,gOAtJVuB,YAAOjB,KAAKb,MAAMmD,4BAAXC,EAAoBC,YAC5BvB,QACK,IAAIwB,MAAM,2DAEbxB,SAjGU9C,EAAMuE,WAuP/B,SAASC,KC7RT,SAASC,EACLC,EACA5D,GAEmB,mBAAR4D,EACPA,EAAI5D,GACG4D,IACPA,EAAIL,QAAUvD,YAIE6D,EACpBC,EACAC,UAOO7E,WAAc,kBACL,MAAR4E,GAAwB,MAARC,EACT,KAEJ,SAACC,GACJL,EAAOG,EAAME,GACbL,EAAOI,EAAMC,MAElB,CAACF,EAAMC,aCtBUE,EAAmB/D,OAC/BoB,EAA8BpB,EAA9BoB,UAA8BpB,EAArBgE,MAAAA,aAAQ,WAClB,CACHC,SAA6B,iBAAZ7C,EAAuBA,EAFNpB,EAATkE,OAEgC,EAAI9C,EAC7D+C,OAAQH,EAAMI,yBACdC,MAAOL,EAAMM,iBFsRpBvE,EAAmBwE,aAAe,KAC3B,EACJlE,cAAc,EACdD,eAAe,EACfF,QAAQ,EACRmB,OAAO,EACPC,MAAM,EAENO,QAAS2B,EACTvB,WAAYuB,EACZrB,UAAWqB,EAEXpB,OAAQoB,EACRlB,UAAWkB,EACXjB,SAAUiB,GGxTd,IAeagB,EAAS,SAAC1C,GAInBA,EAAK2C,UAAY3C,EAAK2C,WCdpBC,EAAW,SAACC,UAA4BC,KAAKC,MAAMF,kBAEjCG,EACpB9E,EACA+E,YADA/E,IAAAA,EAA2B,CAAC,cAG4C+E,GAAW,OAA3Ed,SAAAA,aAAW,UAAKE,OAAAA,aDTb,qCCS+CE,MAAAA,aAAQ,WAE/CW,MAAMC,QAAQjF,GAASA,EAAQ,CAACA,IAG9CkF,KAAI,SAACC,OACIC,EAAwC,iBAAbnB,EAAwBA,EAAWS,EAAST,GACvEoB,EAAkC,iBAAVhB,EAAqBA,EAAQK,EAASL,UAC1Dc,MAAgBC,MAAqBjB,MAAUkB,KAE5DC,KAAK,KCTd,SAASC,EAAYzD,UAJrB,SAAuBA,UACXA,GAAQA,EAAK0D,eAAkBC,SAI3BD,CAAc1D,GACf4D,aAAeC,OAkE9B,SAASC,EAAkBC,EAAqC/D,MACvDA,OACCgE,EAvCV,SACID,EACA/D,OAIIgE,EAFEC,EAAOjE,EAAKkE,wBACZC,EAAkBV,EAAYzD,MAGhCA,EAAKoE,cACLJ,EAAYhE,EAAKoE,kBACd,KACGC,EAAgBF,EAAgBG,iBAAiBtE,GACvDgE,EAAYK,EAAcE,iBAAiB,sBAAwBF,EAAcE,iBAAiB,iBAGlGC,EAAU,EACVC,EAAU,KAEVT,GAA2B,SAAdA,GAA6C,iBAAdA,EAAwB,KAC9DU,EAAkBV,EAAUW,MAAM,KAAK,GAAGA,MAAM,KAAK,GAAGA,MAAM,KACpEH,EAAUI,SAASF,EAAgB,GAAI,IACvCD,EAAUG,SAASF,EAAgB,GAAI,WAGnCX,OACC,4BACoBI,EAAgBU,WAAaL,EAAUP,EAAKa,gBAChE,8BACqBb,EAAKa,KAAOb,EAAKc,MAAQP,aAC9C,0BACoBL,EAAgBa,YAAcP,EAAUR,EAAKgB,yCAG5ChB,EAAKgB,IAAMhB,EAAKiB,OAAST,UAMrCU,CAAkBpB,EAAW/D,GAC3CgE,IACAhE,EAAKkC,MAAMkD,gBAAkBpB,EAC7BhE,EAAKkC,MAAM8B,UAAYA,IAI/B,IAAMqB,EAAQnI,cAA2C,SAACgB,EAAO0D,OAEzDR,EAUAlD,EAVAkD,WAUAlD,EATA6F,UAAAA,aAAY,SACRuB,EAQJpH,KAPAgE,EAOAhE,EAPAgE,QAOAhE,EANAoB,QAAAA,aAAU,IACVS,EAKA7B,EALA6B,QACAM,EAIAnC,EAJAmC,UACAC,EAGApC,EAHAoC,OACAG,EAEAvC,EAFAuC,SACG8E,IACHrH,6FAEEmD,EAAUnE,SAAa,MACvBsI,EAAwB3D,EAAYT,EAAiBQ,IAAKP,GAC1DoE,EAAY5D,EAAW2D,EAAuB5D,GAsD9C8D,EAAiBxI,eAAkB,WACjCmE,EAAQE,SACRuC,EAAkBC,EAAW1C,EAAQE,WAE1C,CAACwC,WAEJ7G,aAAgB,eAERoI,GAAwB,SAAdvB,GAAsC,UAAdA,OAIhC4B,EAtJd,SAAkBC,EAAkBC,OAC5BvG,WACKwG,sCAAaC,2BAAAA,sBACZC,EAAQ,WAEVJ,EAAKK,MAAMC,EAAMH,IAErBI,aAAa7G,GACbA,EAAU4B,WAAW8E,EAAOH,mBARAA,IAAAA,EAAO,KAWvCC,EAAUM,MAAQ,WACdD,aAAa7G,IAGVwG,EAuIkBO,EAAS,WACtBhF,EAAQE,SACRuC,EAAkBC,EAAW1C,EAAQE,YAIvC4C,EAAkBV,EAAYpC,EAAQE,gBAC5C4C,EAAgBmC,iBAAiB,SAAUX,GACpC,WACHA,EAAaS,QACbjC,EAAgBoC,oBAAoB,SAAUZ,OAEnD,CAAC5B,EAAWuB,IAEfpI,aAAgB,WACPoI,GAGDI,MAEL,CAACJ,EAAQI,IAGRxI,gBAACsJ,iBACGpI,UACAiD,QAASA,EACTtB,QA1FwC,SAACC,EAAMH,GACnDiE,EAAkBC,EAAW/D,GAC7B0C,EAAO1C,GAEHD,GACAA,EAAQC,EAAMH,IAsFdQ,UAAWA,EACXF,WAnFe,SAACH,OAEdyG,EAAkBxE,EAAmB,CACvC3C,QAAAA,EACA8C,KAAM,QACNF,WAAYA,GAAOI,0BAJRJ,MAAAA,SAAAA,EAAOI,2BFhHjB,mCEuHLtC,EAAKkC,MAAMwE,iBAAmB1D,EAAiB,oBAAqByD,GACpEzG,EAAKkC,MAAMyE,WAAa3D,EAAiB,YAAayD,GAEtDzG,EAAKkC,MAAMkD,gBAAkB,OAC7BpF,EAAKkC,MAAM8B,UAAY,QAwEnB1D,OArEsC,SAACN,OAErCyG,EAAkBxE,EAAmB,CACvC3C,QAAAA,EACA8C,KAAM,OACNF,WAAYA,GAAOI,0BAJRJ,MAAAA,SAAAA,EAAOI,2BF3HnB,mCEkIHtC,EAAKkC,MAAMwE,iBAAmB1D,EAAiB,oBAAqByD,GACpEzG,EAAKkC,MAAMyE,WAAa3D,EAAiB,YAAayD,GAEtD3C,EAAkBC,EAAW/D,GAEzBM,GACAA,EAAON,IAwDPS,SApD0C,SAACT,GAE/CA,EAAKkC,MAAMwE,iBAAmB,GAC9B1G,EAAKkC,MAAMyE,WAAa,GAEpBlG,GACAA,EAAST,OA+CLsF,EACJhG,QAASA,GACLiG,IAEH,SAAC/G,EAAOoI,UACL1J,eAAmBkE,KACfQ,IAAK6D,EACLvD,SACI2E,WAAsB,WAAVrI,GAAuB8G,OAAoBwB,EAAX,UACzC5E,KACCd,EAAiBlD,MAAMgE,QAE5B0E,UAOvBvB,EAAM0B,YAAc,QC9NpB,IAAMC,EAAU,SAAC9I,UACbhB,qCACI+J,QAAQ,YACRC,UAAU,QACVhF,MAAO,CACHiF,SAAU,GACVC,gBAAiB,EACjBC,WAAY,OACZtC,MAAO,MACPG,OAAQ,MACRoC,QAAS,eACTC,KAAM,eACNC,WAAY,IAEZtJ,KAINuJ,EAAsB,kBACxBvK,gBAAC8J,OACG9J,wBACIwK,EAAE,mIAMRC,EAAwB,kBAC1BzK,gBAAC8J,OACG9J,wBAAMwK,EAAE,wDAIVE,EAAsB,kBACxB1K,gBAAC8J,OACG9J,wBACIwK,EAAE,mOAORG,EAAqB,kBACvB3K,gBAAC8J,OACG9J,wBACIwK,EAAE,yHC5CDI,EAAW,CACpBC,SAAU,EACVC,SAAS,EACTC,iBAAiB,EACjBC,2BAA2B,EAC3BC,QAAS,UACTC,iBAAkB,IAClBC,YD2CyD,cAChDvB,EACTwB,QAASpL,gBAACuK,QACVc,QAASrL,gBAACyK,QACVa,MAAOtL,gBAAC0K,QACRa,KAAMvL,gBAAC2K,SC/CPa,aAAc,CAAE7K,SAAU,SAAUC,WAAY,QAChD0I,oBAAqBnB,EACrBsD,mBAAoB,CAChBpJ,MAAO,IACPC,KAAM,eCdEoJ,EACZC,UAEOC,OAAOC,QAAQF,GAAQG,QAC1B,SAACC,uBACMA,gBACIC,kBAEX,IAIR,IAAaC,EAKU,SAAChB,iCAA4CA,GCN9DiB,EAAUR,EAAW,CACvBS,KAAM,CACFnE,OAAQ,GAEZoE,QAAS,CACLpE,OAAQ,UAaVqE,EAAWrM,cAAgD,SAACgB,EAAO0D,OAC7DR,EAAmClD,EAAnCkD,SAAckE,EAAqBpH,KAAbuC,EAAavC,EAAbuC,SAExB+I,EAAatM,SAA6B,MAE1CmE,EAAUnE,SAA6B,MACvCuI,EAAY5D,EAAWD,EAAKP,GAE5BoI,EAAiB,kBAAOD,EAAWjI,QAAUiI,EAAWjI,QAAQmI,aAAe,UA4CjFxM,gBAACsJ,MACOlB,EACJhH,iBACAyB,QA7CwC,SAACC,GAC7CA,EAAKkC,MAAMgD,OApBG,OAiEV7E,UA3B4C,SAACL,GACjDA,EAAKkC,MAAMgD,OAAS,QA2BhB/E,WA3Ce,SAACH,OACd2J,EAAcF,MAE6BxH,EAAmB,CAChE3C,QA1BI,IA2BJ8C,KAAM,UAFQuG,IAAVxG,SAA8BE,IAAAA,OAKtCrC,EAAKkC,MAAMyG,mBACuB,iBAAvBA,EAAkCA,EAAwBA,OAErE3I,EAAKkC,MAAMgD,OAAYyE,OACvB3J,EAAKkC,MAAMI,yBAA2BD,GAAU,IAgC5C/B,OAzBsC,SAACN,GAC3CA,EAAKkC,MAAMgD,OAAYuE,UAyBnBhJ,SAAUA,EACVD,UAvBc,SAACR,GACnB0C,EAAO1C,SAE0CiC,EAAmB,CAChE3C,QAjDI,IAkDJ8C,KAAM,SAFQuG,IAAVxG,SAA8BE,IAAAA,OAKtCrC,EAAKkC,MAAMyG,mBACuB,iBAAvBA,EAAkCA,EAAwBA,OACrE3I,EAAKkC,MAAMgD,OAxDG,MAyDdlF,EAAKkC,MAAMI,yBAA2BD,GAAU,IAa5ChB,QAASA,EACT/B,QAtEI,MAwEH,SAACd,EAAOoI,UACL1J,qCACI0E,IAAK6D,EACLmE,UAAWC,EAAKT,EAAQC,KAAqC,YAAV7K,GAAlB4K,EAAQE,SACzCpH,SACI4H,cAAe,MACfC,SAAU,SACVC,UAhFF,MAiFErD,WAAY3D,EAAiB,WACf,YAAVxE,GAAuB,CACvBuL,SAAU,cAEA,WAAVvL,IACC8G,GAAU,CACPuB,WAAY,YAGpBD,GAEJ1J,uBACI0E,IAAK4H,EACLI,UDnGH,4BCqGG1H,MAAO,CAAEoF,QAAS,OAAQvC,MAAO,SAEhC3D,UAQzBmI,EAASxC,YAAc,WCrHvB,IAAMhD,EAAsD,CACxDkG,MAAO,OACPnF,KAAM,QACNoF,OAAQ,KACRjF,IAAK,QAWIkF,EAAyB,SAACzB,wBACpB/K,EAAmB+K,ICzBhCzL,EAAO,sBAWWmN,EACpBC,EACAC,UAGOD,EAAMrB,QAAO,SAACC,EAAKrD,UAClBA,MAAAA,EACOqD,EAGJ,sCAA4BlD,2BAAAA,sBACzBwE,YAAaxE,GACfuE,IAA8C,IAAhCC,EAAOC,QAAQF,IAC7BC,EAAOE,KAAKH,GAGhBrB,EAAIhD,MAAMlH,KAAMwL,GAChB3E,EAAKK,MAAMlH,KAAMwL,MAEtBtN,GC5BP,IAEMyN,EAAsC,oBAAX7G,OAAyB3G,kBAAwBA,qBAE1DyN,EACpBC,OAEMhJ,EAAM1E,SAAa0N,UACzBF,GAAkB,WACd9I,EAAIL,QAAUqJ,KAEX1N,eACH,kBAEQ0E,EAAJ,kCACJ,IChBR,MAeMiJ,EAAW3N,cAAgD,SAACgB,EAAO0D,OAEjER,EAQAlD,EARAkD,SACAwI,EAOA1L,EAPA0L,UACAxB,EAMAlK,EANAkK,mBAMAlK,EALAgK,0BAAAA,gBACA4C,EAIA5M,EAJA4M,QACAC,EAGA7M,EAHA6M,GACAC,EAEA9M,EAFA8M,OAEA9M,EADA+M,cAAAA,aAAgB,KAGdC,EAAgBhO,WAEhBiO,EAAcR,GAAiB,WAC7BG,GACAA,6BAIFM,EAAmBT,GAAiB,SAACU,GAClCP,GAAoC,MAAzBO,IAIZH,EAAc3J,SACd4E,aAAa+E,EAAc3J,SAE/B2J,EAAc3J,QAAUL,YAAW,WAC/BiK,EAAY,KAAM,UAAWJ,KAC9BM,OAGPnO,aAAgB,kBACR8N,GACAI,EAAiBhD,GAGd,WACC8C,EAAc3J,SACd4E,aAAa+E,EAAc3J,YAGpC,CAACyJ,EAAM5C,EAAkBgD,QAMtBE,EAAc,WACZJ,EAAc3J,SACd4E,aAAa+E,EAAc3J,UAQ7BgK,EAAerO,eAAkB,WACX,MAApBkL,GACAgD,EAAoC,GAAnBhD,KAEtB,CAACA,EAAkBgD,WAgBtBlO,aAAgB,eACPgL,GAA6B8C,SAC9BnH,OAAOyC,iBAAiB,QAASiF,GACjC1H,OAAOyC,iBAAiB,OAAQgF,GAEzB,WACHzH,OAAO0C,oBAAoB,QAASgF,GACpC1H,OAAO0C,oBAAoB,OAAQ+E,MAK5C,CAACpD,EAA2BqD,EAAcP,IAGzC9N,qCACI0E,IAAKA,GACDqJ,GACJrB,UAAWC,ELnGT,qBKmGyCD,GAC3C4B,aAjC0D,SAACC,GAC3DR,EAAcO,cACdP,EAAcO,aAAaC,GAE/BH,KA8BII,aA3B0D,SAACD,GAC3DR,EAAcS,cACdT,EAAcS,aAAaD,GAE/BF,OAyBKnK,MAKbyJ,EAAS9D,YAAc,WCtHvB,IAAMqC,EAAUR,EAAW,CACvBS,SACI/B,QAAS,OACTqE,SAAU,OACVC,SAAU,KfNR,4BeOkB,CAChBA,SAAU,UACVC,SAAU,cAKhBC,EAAkBC,cAAiD,WAA0BnK,OAAvBgI,IAAAA,UAAc1L,4BACtFhB,qCAAK0E,IAAKA,EAAKgI,UAAWC,EAAKT,EAAQC,KAAMO,IAAgB1L,OAGjE4N,EAAgB/E,YAAc,kBChB9B,IAAMqC,EAAUR,EAAW,CACvBS,KAAM,CACF2C,gBAAiB,UACjB7E,SAAU,WACV8E,WAAY,KACZC,cAAe,YACfC,MAAO,OACPC,WAAY,SACZC,QAAS,WACTC,aAAc,MACdC,UACI,wGAERC,YAAa,CACTC,YAAgB,gBAEX,CACLT,gBAAiB,WAErB1D,QAAS,CACL0D,gBAAiB,WAErBxD,MAAO,CACHwD,gBAAiB,WAErBzD,QAAS,CACLyD,gBAAiB,WAErBvD,KAAM,CACFuD,gBAAiB,WAErBU,QAAS,CACLpF,QAAS,OACT8E,WAAY,SACZC,QAAS,SAEbM,OAAQ,CACJrF,QAAS,OACT8E,WAAY,SACZQ,WAAY,OACZH,YAAa,OACbI,YAAa,UAMfC,EAAwBf,cAA+C,SAAC7N,EAAO6O,OAG7EL,EAOAxO,EAPAwO,QAGAvE,EAIAjK,EAJAiK,QACAF,EAGA/J,EAHA+J,gBACA/F,EAEAhE,EAFAgE,MACA0H,EACA1L,EADA0L,UAGEoD,EAFF9O,EALAmK,YAOqBF,GAErBwE,EAJAzO,EANAyO,aAWkB,mBAAXA,IACPA,EAASA,EANTzO,EARA6M,KAkBA7N,gBAAC4O,GACGlK,IAAKmL,EACLE,KAAK,2BAxBO,qBA0BZ/K,MAAOA,EACP0H,UAAWC,EP5DP,uBO8DAV,EAAmChB,GACnCiB,EAAQC,KAERD,EAAQjB,GACRyB,GAF0B3B,GAAmB+E,GAA1C5D,EAAQoD,cAKftP,uBAAK6N,GApCO,qBAoCcnB,UAAWR,EAAQsD,SACvCzE,EAAyB,KAAP+E,EACnBN,GAEJC,GAAUzP,uBAAK0M,UAAWR,EAAQuD,QAASA,OAKxDG,EAAsB/F,YAAc,wBAEpC,kBAAemG,OAAKJ,GClFdjE,GAASD,EAAW,CACtBuE,YAAa,CACTpI,MAAO,OACPqI,SAAU,WACVpJ,UAAW,gBACXiB,IAAK,EACLgF,MAAO,EACPC,OAAQ,EACRpF,KAAM,EACN+G,SAAU,WAYZwB,GAA4C,SAACnP,OACzCoB,EAAUgO,aACkBC,YAAS,GAApCC,OAAWC,OAEZtC,EAAmDf,EAAsB,CAC3ElM,EAAMwP,MAAM5C,QACZ5M,EAAM4M,UASJ6C,EAAqBC,eAAY,WACnCtO,EAAQiC,QAAUL,YAAW,WACzBuM,GAAa,SAACI,UAASA,OACxB,OACJ,IAEHC,aACI,kBAAM,WACExO,EAAQiC,SACR4E,aAAa7G,EAAQiC,YAG7B,QN9C0BmH,EMiDtBgF,EAAkExP,EAAlEwP,MAAgBK,EAAkD7P,EAA3DkL,UAA2DlL,EAAtCuD,UAAAA,aAAYqL,KAE1C1D,EAAU4E,WAAQ,kBNrCS,SACjC5E,YAAAA,IAAAA,EAA4C,QAEtC6E,EAAoD,CACtDC,eAAe,EACfC,gCAAgC,EAChCC,mCAAmC,EACnCC,+BAA+B,EAC/BC,kCAAkC,EAClCC,8BAA8B,EAC9BC,iCAAiC,UAE7B1F,OAAO2F,KAAKrF,GACfsF,QAAO,SAACC,UAASV,EAAiBU,MAClC3F,QAAO,SAAC4F,EAAKD,qBAAcC,UAAMD,GAAMvF,EAAQuF,SAAS,IMuB/BE,CAAsBd,KAAa,CAACA,IAG9D/C,EAcA0C,EAdA1C,KACAC,EAaAyC,EAbAzC,cACAzE,EAYAkH,EAZAlH,oBACAsI,EAWApB,EAXAoB,gBACAnG,EAUA+E,EAVA/E,mBACAT,EASAwF,EATAxF,0BACS6G,EAQTrB,EARAsB,QACSC,IAOTvB,kMAEEjH,KACF1C,WNvE0B2E,EMuEGwG,EAAWxG,aNtEZ,WAA5BA,EAAa5K,WACNiG,EAAU2E,EAAa5K,YAE3BiG,EAAU2E,EAAa7K,WMoE1ByB,QAASqJ,GACNmG,GAGHE,EAAUD,EACS,mBAAZC,IACPA,EAAUA,EAAQE,EAAWnE,GAAImE,EAAWxC,cAG1CyC,EACF,CAAC,UAAW,YAAa,SAAU,YACrCnG,QACE,SAACC,EAAKmG,qBACCnG,UACFmG,GAAShF,EAAsB,CAAClM,EAAMwP,MAAM0B,GAAgBlR,EAAMkR,IAAiBF,EAAWnE,UAEnG,WAIA7N,gBAACqM,MAAaiE,EAAW/M,SAAU0O,EAAU1O,UACzCvD,gBAAC2N,GACGG,KAAMA,EACND,GAAImE,EAAWnE,GACf7C,0BAA2BA,EAC3BE,iBAAkB8G,EAAW9G,iBAC7BwB,UAAWC,EACPhB,GAAOsE,YACP/D,EAAQC,KACRD,EAAQe,EAAuB+E,EAAWxG,gBAE9CuC,cAAeA,EACfH,QAASK,GAETjO,gBAACsJ,mBACOC,GACJrI,aACI4M,EACJ1K,OAAQ6O,EAAU7O,OAClBG,SAAUkN,EACV5N,QAASoP,EAAUpP,QAGnBM,UAAW+J,EAAsB,CAAC+E,EAAU9O,UAvFD,WACnDnC,EAAMwP,MAAM2B,cACZlE,EAAY,KAAM,aAAcjN,EAAMwP,MAAM3C,MAqFmCmE,EAAWnE,MAEhFiE,GAAkC9R,gBAACuE,mBAAcyN,QCvHjErG,GAASD,EAAW,CACtBS,SACIiG,UAAW,aACXhI,QAAS,OACTiI,UAAW,OACXnC,SAAU,QACVoC,OAAQ,KACRtK,OAAQ,OACRH,MAAO,OACP4B,WAAY3D,EAAiB,CAAC,MAAO,QAAS,SAAU,OAAQ,aAAc,CAC1Eb,SAAU,IACVE,OAAQ,SAIZyH,cAAe,wCACI,CACfuC,QAAYoD,UACZ9I,WAAY,4BAEhB+I,+BlBjCI,+BkBkCkB,CAClB3K,MAAO,OACP2K,iCAGRC,gDACuB,CACftD,QAAYoD,cAGpBxK,IAAK,CACDA,IAAQwK,OACRG,cAAe,UAEnB1F,OAAQ,CACJA,OAAWuF,OACXG,cAAe,kBAEnB9K,SACIA,KAAS2K,UlBpDP,4BkBqDkB,CAChBrD,WAAY,gBlBvDZ,+BkByDkB,CAClBtH,KAAS+K,WAGjB5F,UACIA,MAAUwF,UlB7DR,4BkB8DkB,CAChBrD,WAAY,clBhEZ,+BkBkEkB,CAClBnC,MAAU4F,WAGlBC,YACIhL,KAAM,MACNd,UAAW,uBlBvET,4BkBwEkB,CAChBoI,WAAY,mBA4BTc,QAhB6C,SAAChP,SACDA,EAAhDkL,QAAAA,aAAU,KAAIV,EAAkCxK,EAAlCwK,aAAcqH,EAAoB7R,EAApB6R,MAAO3O,EAAalD,EAAbkD,SAErC4O,EAAoBnG,ET7EP,8BS+EfhB,GAAOH,EAAa7K,UACpBgL,GAAOH,EAAa5K,YAEpB+K,GAAOQ,KACPD,EAAQ8E,cACR9E,0BAAgCzL,EAAmB+K,IAH7BqH,GAAnBlH,GAAO8G,kBAMPzS,uBAAK0M,UAAWoG,GAAoB5O,MCjFzC6O,GAAY,SACdC,WAE8C,iBAArBA,GAAiCC,iBAAeD,KAgBvEE,0BACUlS,8BACFA,0BAsBQ,SACdgS,EACAG,eAAAA,IAAAA,EAAiC,IAE7BH,MAAAA,QACM,IAAI1O,MAAM,oDAGd8O,EAAOL,GAAUC,GAAoBA,EAAmBG,EAExD3D,EAAuCuD,GAAUC,GACjDA,EAAiBxD,QACjBwD,EAEEvB,EAAsC2B,EAAtC3B,IAAK4B,EAAiCD,EAAjCC,iBAAqBtN,IAAYqN,8BAExCE,EAAkBzS,EAAU4Q,GAC5B5D,EAAKyF,EAAmB7B,GAAsB,IAAI8B,MAAOC,UAAY5N,KAAK6N,SAE1EC,EXjBV,SAAC3N,EAAc/E,UACf,SAAC2S,EAA2BC,mBAAAA,IAAAA,GAAoB,GACxCA,OAEQhJ,EAAiB+I,MAClB3S,EAAM2S,MACN5N,EAAQ4N,IAIN,qBAATA,GApDuCE,EAqDc7S,EAAMkK,kBApD7D4I,EAAiB,SAACC,SAAkD,iBAAdA,GAAwC,OAAdA,IAD7DC,EAqDUjO,EAAQmF,kBAlDC8I,EACxCF,EAAeD,GAAuBA,EACnCjJ,EAASM,kBAmDC,uBAATyI,EA5CkB,SAACK,EAAsBH,OAC3CI,EAAK,SAACC,EAAWC,UAAoBA,EAAMC,MAAK,SAACC,iBAAaH,IAASG,aAEzEJ,EAAGD,EAAiB,CAAC,SAAU,WACxBA,EAGPC,EAAGD,EAAiB,CAAC,gBAEdpJ,EAASa,sBACRwI,EAAGJ,EAAe,CAAC,YAAcA,KAClCG,GAIPC,EAAGJ,EAAe,CAAC,SAAU,WACtBA,EAGPI,EAAGJ,EAAe,CAAC,gBAEZjJ,EAASa,sBACToI,GAIJjJ,EAASa,mBAmBD6I,CAAsBvO,EAAQ0F,mBAAoBzK,EAAMyK,oBAG5D1F,EAAQ4N,IAAS3S,EAAM2S,IAAU/I,EAAiB+I,GA5DrC,IAACK,EAAsBH,EACzCC,GW0DaS,CAAMxO,EAASiD,EAAKhI,OAC7BwP,KACF3C,GAAAA,GACG9H,GACHyJ,QAAAA,EACA1B,MAAM,EACN1B,SAAS,EACT+F,cAAc,EACdrH,QAAS4I,EAAO,WAChBjE,OAAQiE,EAAO,UACf5B,QAAS4B,EAAO,WAChBzI,QAASyI,EAAO,WAChBlI,aAAckI,EAAO,gBACrB1I,0BAA2B0I,EAAO,6BAClCxI,iBAAkBwI,EAAO,oBACzB3I,gBAAiB2I,EAAO,mBACxBpK,oBAAqBoK,EAAO,uBAC5BjI,mBAAoBiI,EAAO,sBAC3B9B,gBAAiB8B,EAAO,mBAAmB,GAC3CvI,YAAauI,EAAO,eAAe,GACnC1O,MAAO0O,EAAO,SAAS,GACvB3F,cAAe2F,EAAO,iBAAiB,GACvChH,UAAWC,EAAK3D,EAAKhI,MAAM0L,UAAW3G,EAAQ2G,oBAG9C8D,EAAM1F,UACN0F,EAAMtF,sBAAmBtB,KAGxBlH,UAAS,SAACpB,WACesI,IAArByJ,GAAkCrK,EAAKhI,MAAMqS,kBAAqBA,EAAkB,KAC/EmB,EAAkB,SAACN,UACrBZ,EAAkBY,EAAKrG,KAAOA,EAAKqG,EAAK1E,UAAYA,GAElDiF,EAAUnT,EAAMoT,MAAMC,UAAUH,IAAoB,EACpDI,EAAStT,EAAMuT,OAAOF,UAAUH,IAAoB,KACtDC,GAAWG,SACJtT,SAIR0H,EAAK8L,wBACLxT,GACHoT,gBAAWpT,EAAMoT,OAAOlE,UAIzB3C,wBAOmB,SAACvM,UACRA,EAAXuT,OACGE,QAAU/L,EAAK6B,SACf7B,EAAKgM,oBAAoB1T,GAE7B0H,EAAKiM,aAAa3T,mBAML,SAACA,OACboT,EAAkBpT,EAAlBoT,aACJA,EAAMK,OAAS,OAERzT,GACHuT,iBAJkBvT,EAAXuT,QAIaH,EAAM,KAC1BA,MAAOA,EAAMlU,MAAM,EAAGkU,EAAMK,UAG7BzT,yBAWoB,SAACA,MACxBA,EAAMuT,OAAOT,MAAK,SAACF,UAAUA,EAAKpG,MAAQoG,EAAK/B,uBACxC7Q,MAGP4T,GAAS,EACTC,GAAS,EAEW7T,EAAMuT,OAAO/I,QACjC,SAACC,EAAK1H,UAAY0H,GAAO1H,EAAQyJ,MAAQzJ,EAAQyG,QAAU,EAAI,KAC/D,KAGoB9B,EAAK6B,WAEzBsK,GAAS,OAGPN,EAASvT,EAAMuT,OAAO3O,KAAI,SAACgO,UACxBgB,GAAYhB,EAAKpJ,UAAWqK,OAwBrBjB,IAvBRgB,GAAS,EAEJhB,EAAK9H,SAON8H,EAAKtG,SACLsG,EAAKtG,QAAQ,KAAM,WAAYsG,EAAKrG,IAGpC7E,EAAKhI,MAAM4M,WACN5M,MAAM4M,QAAQ,KAAM,WAAYsG,EAAKrG,SAIvCqG,GACHpG,MAAM,UAfCoG,GACH/B,cAAc,oBAqBlB7Q,GAAOuT,OAAAA,0BAMmC,SAAC/R,EAAMH,EAAa8O,OACrE5Q,EAAU4Q,SACL,IAAInN,MAAM,4DAGf5B,UAAS,kBAAiB,CAC3BmS,SADaA,OACE3O,KAAI,SAACgO,UAAUA,EAAKrG,KAAO4D,OAAWyC,GAAM9H,SAAS,SAAc8H,8BAOlC,SAAC3F,EAAO6G,EAAQ3D,GAGhEzI,EAAKhI,MAAM4M,WACN5M,MAAM4M,QAAQW,EAAO6G,EAAQ3D,OAGhC4D,OAAyBzL,IAAR6H,IAElB/O,UAAS,gBAAWgS,IAAAA,YAAa,CAClCG,SADaA,OACE3O,KAAI,SAACgO,UACXmB,GAAkBnB,EAAKrG,KAAO4D,OAIRyC,EAApBA,EAAK9H,SAAqB0B,MAAM,IAAqBqE,cAAc,SAH1D+B,MAKpBQ,MAAOA,EAAMlD,QAAO,SAAC0C,UAASA,EAAKrG,KAAO4D,0BAOA,SAACA,OAEzC6D,EAAatM,EAAK1H,MAAMuT,OAAOU,MAAK,SAACrB,UAASA,EAAKrG,KAAO4D,KAC5D5Q,EAAU4Q,IAAQ6D,GAAcA,EAAW1H,SAC3C0H,EAAW1H,QAAQ,KAAM,aAAc6D,KAGtC+D,iBAAiB,KAAM,aAAc/D,wBAUU,SAAC3O,EAAM2O,OACtD5Q,EAAU4Q,SACL,IAAInN,MAAM,2DAGf5B,UAAS,SAACpB,OACLmU,EAAWzM,EAAKiM,kBACf3T,GACHuT,OAAQvT,EAAMuT,OAAOrD,QAAO,SAAC0C,UAASA,EAAKrG,KAAO4D,eAGxB,IAA1BgE,EAASf,MAAMK,OACRU,EAGJzM,EAAKgM,oBAAoBS,OAtPpCvV,wBAAkB8I,EAAK9I,gBACvBC,sBAAgB6I,EAAK7I,gBAEhBmB,MAAQ,CACTuT,OAAQ,GACRH,MAAO,GACPgB,aAAc,CACVxV,gBAAiB8I,EAAK9I,gBAAgByV,WACtCxV,cAAe6I,EAAK7I,cAAcwV,yCAkP9C1R,OAAA,sBACYyR,EAAiB7T,KAAKP,MAAtBoU,eAC+D7T,KAAKb,MAApE4U,IAAAA,QAAS1R,IAAAA,aAAU2O,MAAAA,oBAAegD,WAAAA,aAAa,KAAI3J,IAAAA,QAErD4J,EAAQjU,KAAKP,MAAMuT,OAAO/I,QAAyB,SAACC,EAAK1H,SACrD0R,EAAWtV,EAAmB4D,EAAQmH,0BAGrCO,UACFgK,aAHsBhK,EAAIgK,IAAa,IAGJ1R,UAEzC,IAEG2R,EAAYpK,OAAO2F,KAAKuE,GAAO5P,KAAI,SAAC+P,OAChCpB,EAASiB,EAAMG,UAGjBjW,gBAACkW,IACGzE,IAAKwE,EACLpD,MAAOA,EACPrH,aALeqJ,KAKYrJ,aAC3BU,QAASA,GAER2I,EAAO3O,KAAI,SAACsK,UACTxQ,gBAACmQ,IACGsB,IAAKjB,EAAM3C,GACX2C,MAAOA,EACPtE,QAASA,EACT3H,UAAWsR,EAAWrF,EAAMvF,SAC5B2C,QAAS5K,EAAKwS,iBACd3S,QAASG,EAAKhC,MAAM6B,QACpBO,OAAQJ,EAAKhC,MAAMoC,OACnBG,SAAU2J,EAAsB,CAAClK,EAAKmT,kBAAmBnT,EAAKhC,MAAMuC,UAAWiN,EAAM3C,IACrF1K,UAAW+J,EAAsB,CAAClK,EAAKoT,mBAAoBpT,EAAKhC,MAAMmC,WAAYqN,EAAM3C,oBAQxG7N,gBAACqW,EAAgBC,UAASxV,MAAO4U,GAC5BxR,EACA0R,EAAUW,eAAaP,EAAWJ,GAAWI,+CAvR/CnU,KAAKb,MAAM6J,UAAYD,EAASC,gBAjBhBtG,+JCpCOiS,aAAWH"}