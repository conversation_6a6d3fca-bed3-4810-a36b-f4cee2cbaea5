{"version": 3, "file": "notistack.esm.js", "sources": ["../src/SnackbarContext.ts", "../src/utils/index.ts", "../src/transitions/Transition/Transition.tsx", "../src/transitions/useForkRef.ts", "../src/transitions/getTransitionProps.ts", "../src/transitions/utils.ts", "../src/transitions/createTransition.ts", "../src/transitions/Slide/Slide.tsx", "../src/utils/defaultIconVariants.tsx", "../src/SnackbarProvider/merger.ts", "../src/utils/styles.ts", "../src/transitions/Collapse/Collapse.tsx", "../src/SnackbarItem/utils.ts", "../src/utils/createChainedFunction.ts", "../src/utils/useEventCallback.ts", "../src/SnackbarItem/Snackbar.tsx", "../src/SnackbarContent/SnackbarContent.tsx", "../src/ui/MaterialDesignContent/MaterialDesignContent.tsx", "../src/SnackbarItem/SnackbarItem.tsx", "../src/SnackbarContainer/SnackbarContainer.tsx", "../src/utils/warning.ts", "../src/SnackbarProvider/SnackbarProvider.tsx", "../src/useSnackbar.ts"], "sourcesContent": ["import React from 'react';\nimport { ProviderContext } from './types';\n\nconst noOp = () => {\n    return '';\n};\n\nexport default React.createContext<ProviderContext>({\n    enqueueSnackbar: noOp,\n    closeSnackbar: noOp,\n});\n", "import { InternalSnack } from '../types';\n\nexport const breakpoints = {\n    downXs: '@media (max-width:599.95px)',\n    upSm: '@media (min-width:600px)',\n};\n\nconst capitalise = (text: string): string => text.charAt(0).toUpperCase() + text.slice(1);\n\nexport const originKeyExtractor = (anchor: InternalSnack['anchorOrigin']): string =>\n    `${capitalise(anchor.vertical)}${capitalise(anchor.horizontal)}`;\n\nexport const isDefined = (value: string | null | undefined | number): boolean => !!value || value === 0;\n", "/**\n * BSD 3-Clause License\n *\n * Copyright (c) 2018, React Community\n * Forked from React (https://github.com/facebook/react) Copyright 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * * Redistributions of source code must retain the above copyright notice, this\n * list of conditions and the following disclaimer.\n *\n * * Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * * Neither the name of the copyright holder nor the names of its\n * contributors may be used to endorse or promote products derived from\n * this software without specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE\n * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL\n * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\n * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\n * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,\n * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\nimport React from 'react';\nimport { TransitionComponentProps, TransitionStatus } from '../../types';\n\nconst UNMOUNTED = 'unmounted';\nconst EXITED = 'exited';\nconst ENTERING = 'entering';\nconst ENTERED = 'entered';\nconst EXITING = 'exiting';\n\ninterface State {\n    status: TransitionStatus;\n}\n\ninterface NextCallback {\n    (): void;\n    cancel?: () => void;\n}\n\nclass Transition extends React.Component<TransitionComponentProps, State> {\n    appearStatus: TransitionStatus | null;\n\n    nextCallback: NextCallback | null;\n\n    constructor(props: TransitionComponentProps) {\n        super(props);\n\n        const { appear } = props;\n\n        let initialStatus: TransitionStatus;\n\n        this.appearStatus = null;\n\n        if (props.in) {\n            if (appear) {\n                initialStatus = EXITED;\n                this.appearStatus = ENTERING;\n            } else {\n                initialStatus = ENTERED;\n            }\n        } else if (props.unmountOnExit || props.mountOnEnter) {\n            initialStatus = UNMOUNTED;\n        } else {\n            initialStatus = EXITED;\n        }\n\n        this.state = { status: initialStatus };\n\n        this.nextCallback = null;\n    }\n\n    static getDerivedStateFromProps({ in: nextIn }: TransitionComponentProps, prevState: State) {\n        if (nextIn && prevState.status === UNMOUNTED) {\n            return { status: EXITED };\n        }\n        return null;\n    }\n\n    componentDidMount() {\n        this.updateStatus(true, this.appearStatus);\n    }\n\n    componentDidUpdate(prevProps: TransitionComponentProps) {\n        let nextStatus: TransitionStatus | null = null;\n        if (prevProps !== this.props) {\n            const { status } = this.state;\n\n            if (this.props.in) {\n                if (status !== ENTERING && status !== ENTERED) {\n                    nextStatus = ENTERING;\n                }\n            } else if (status === ENTERING || status === ENTERED) {\n                nextStatus = EXITING;\n            }\n        }\n        this.updateStatus(false, nextStatus);\n    }\n\n    componentWillUnmount() {\n        this.cancelNextCallback();\n    }\n\n    getTimeouts(): { exit: number; enter: number } {\n        const { timeout } = this.props;\n        let enter = timeout;\n        let exit = timeout;\n\n        if (timeout != null && typeof timeout !== 'number' && typeof timeout !== 'string') {\n            exit = timeout.exit;\n            enter = timeout.enter;\n        }\n        return {\n            exit: exit as number,\n            enter: enter as number,\n        };\n    }\n\n    updateStatus(mounting = false, nextStatus: TransitionStatus | null) {\n        if (nextStatus !== null) {\n            this.cancelNextCallback();\n\n            if (nextStatus === ENTERING) {\n                this.performEnter(mounting);\n            } else {\n                this.performExit();\n            }\n        } else if (this.props.unmountOnExit && this.state.status === EXITED) {\n            this.setState({ status: UNMOUNTED });\n        }\n    }\n\n    get node() {\n        const node = this.props.nodeRef?.current;\n        if (!node) {\n            throw new Error('notistack - Custom snackbar is not refForwarding');\n        }\n        return node;\n    }\n\n    performEnter(mounting: boolean) {\n        const { enter } = this.props;\n        const isAppearing = mounting;\n\n        const timeouts = this.getTimeouts();\n\n        if (!mounting && !enter) {\n            this.safeSetState({ status: ENTERED }, () => {\n                if (this.props.onEntered) {\n                    this.props.onEntered(this.node, isAppearing);\n                }\n            });\n            return;\n        }\n\n        if (this.props.onEnter) {\n            this.props.onEnter(this.node, isAppearing);\n        }\n\n        this.safeSetState({ status: ENTERING }, () => {\n            if (this.props.onEntering) {\n                this.props.onEntering(this.node, isAppearing);\n            }\n\n            this.onTransitionEnd(timeouts.enter, () => {\n                this.safeSetState({ status: ENTERED }, () => {\n                    if (this.props.onEntered) {\n                        this.props.onEntered(this.node, isAppearing);\n                    }\n                });\n            });\n        });\n    }\n\n    performExit() {\n        const { exit } = this.props;\n        const timeouts = this.getTimeouts();\n\n        // no exit animation skip right to EXITED\n        if (!exit) {\n            this.safeSetState({ status: EXITED }, () => {\n                if (this.props.onExited) {\n                    this.props.onExited(this.node);\n                }\n            });\n            return;\n        }\n\n        if (this.props.onExit) {\n            this.props.onExit(this.node);\n        }\n\n        this.safeSetState({ status: EXITING }, () => {\n            if (this.props.onExiting) {\n                this.props.onExiting(this.node);\n            }\n\n            this.onTransitionEnd(timeouts.exit, () => {\n                this.safeSetState({ status: EXITED }, () => {\n                    if (this.props.onExited) {\n                        this.props.onExited(this.node);\n                    }\n                });\n            });\n        });\n    }\n\n    cancelNextCallback() {\n        if (this.nextCallback !== null && this.nextCallback.cancel) {\n            this.nextCallback.cancel();\n            this.nextCallback = null;\n        }\n    }\n\n    safeSetState(nextState: State, callback: () => void) {\n        callback = this.setNextCallback(callback);\n        this.setState(nextState, callback);\n    }\n\n    setNextCallback(callback: () => void) {\n        let active = true;\n\n        this.nextCallback = () => {\n            if (active) {\n                active = false;\n                this.nextCallback = null;\n\n                callback();\n            }\n        };\n\n        (this.nextCallback as NextCallback).cancel = () => {\n            active = false;\n        };\n\n        return this.nextCallback;\n    }\n\n    onTransitionEnd(timeout: number, handler: () => void) {\n        this.setNextCallback(handler);\n        const doesNotHaveTimeoutOrListener = timeout == null && !this.props.addEndListener;\n        if (!this.node || doesNotHaveTimeoutOrListener) {\n            setTimeout(this.nextCallback as NextCallback, 0);\n            return;\n        }\n\n        if (this.props.addEndListener) {\n            this.props.addEndListener(this.node, this.nextCallback as NextCallback);\n        }\n\n        if (timeout != null) {\n            setTimeout(this.nextCallback as NextCallback, timeout);\n        }\n    }\n\n    render() {\n        const { status } = this.state;\n\n        if (status === UNMOUNTED) {\n            return null;\n        }\n\n        const {\n            children,\n            // filter props for `Transition`\n            in: _in,\n            mountOnEnter: _mountOnEnter,\n            unmountOnExit: _unmountOnExit,\n            appear: _appear,\n            enter: _enter,\n            exit: _exit,\n            timeout: _timeout,\n            addEndListener: _addEndListener,\n            onEnter: _onEnter,\n            onEntering: _onEntering,\n            onEntered: _onEntered,\n            onExit: _onExit,\n            onExiting: _onExiting,\n            onExited: _onExited,\n            nodeRef: _nodeRef,\n            ...childProps\n        } = this.props;\n\n        return children(status, childProps);\n    }\n}\n\nfunction noop() {\n    //\n}\n\n(Transition as any).defaultProps = {\n    in: false,\n    mountOnEnter: false,\n    unmountOnExit: false,\n    appear: false,\n    enter: true,\n    exit: true,\n\n    onEnter: noop,\n    onEntering: noop,\n    onEntered: noop,\n\n    onExit: noop,\n    onExiting: noop,\n    onExited: noop,\n};\n\nexport default Transition;\n", "/**\n * Credit to MUI team @ https://mui.com\n */\nimport * as React from 'react';\n\n/**\n * passes {value} to {ref}\n *\n * Useful if you want to expose the ref of an inner component to the public API\n * while still using it inside the component.\n * @param ref A ref callback or ref object. If anything falsy, this is a no-op.\n */\nfunction setRef<T>(\n    ref: React.MutableRefObject<T | null> | ((instance: T | null) => void) | null | undefined,\n    value: T | null\n): void {\n    if (typeof ref === 'function') {\n        ref(value);\n    } else if (ref) {\n        ref.current = value;\n    }\n}\n\nexport default function useForkRef<Instance>(\n    refA: React.Ref<Instance> | null | undefined,\n    refB: React.Ref<Instance> | null | undefined\n): React.Ref<Instance> | null {\n    /**\n     * This will create a new function if the ref props change and are defined.\n     * This means react will call the old forkRef with `null` and the new forkRef\n     * with the ref. Cleanup naturally emerges from this behavior.\n     */\n    return React.useMemo(() => {\n        if (refA == null && refB == null) {\n            return null;\n        }\n        return (refValue) => {\n            setRef(refA, refValue);\n            setRef(refB, refValue);\n        };\n    }, [refA, refB]);\n}\n", "import { TransitionDuration } from '../types';\n\ninterface ComponentProps {\n    style?: React.CSSProperties | undefined;\n    /**\n     * number: 400\n     * TransitionDuration: { enter: 200, exit: 400 }\n     */\n    timeout: number | TransitionDuration;\n    mode: 'enter' | 'exit';\n}\n\ninterface TransitionPropsReturnType {\n    duration: number;\n    easing: string | undefined;\n    delay: string | undefined;\n}\n\nexport default function getTransitionProps(props: ComponentProps): TransitionPropsReturnType {\n    const { timeout, style = {}, mode } = props;\n    return {\n        duration: typeof timeout === 'object' ? timeout[mode] || 0 : timeout,\n        easing: style.transitionTimingFunction,\n        delay: style.transitionDelay,\n    };\n}\n", "/**\n * Credit to MUI team @ https://mui.com\n */\nexport const defaultEasing = {\n    // This is the most common easing curve.\n    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',\n    // Objects enter the screen at full velocity from off-screen and\n    // slowly decelerate to a resting point.\n    easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',\n    // Objects leave the screen at full velocity. They do not decelerate when off-screen.\n    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',\n    // The sharp curve is used by objects that may return to the screen at any time.\n    sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',\n};\n\n/**\n * CSS hack to force a repaint\n */\nexport const reflow = (node: Element): void => {\n    // We have to do something with node.scrollTop.\n    // Otherwise it's removed from the compiled code by optimisers\n    // eslint-disable-next-line no-self-assign\n    node.scrollTop = node.scrollTop;\n};\n", "import { defaultEasing } from './utils';\n\ninterface CreateTransitionOptions {\n    duration: string | number;\n    easing?: string;\n    delay?: string | number;\n}\n\nconst formatMs = (milliseconds: number) => `${Math.round(milliseconds)}ms`;\n\nexport default function createTransition(\n    props: string | string[] = ['all'],\n    options?: CreateTransitionOptions\n): string {\n    const { duration = 300, easing = defaultEasing.easeInOut, delay = 0 } = options || {};\n\n    const properties = Array.isArray(props) ? props : [props];\n\n    return properties\n        .map((animatedProp) => {\n            const formattedDuration = typeof duration === 'string' ? duration : formatMs(duration);\n            const formattedDelay = typeof delay === 'string' ? delay : formatMs(delay);\n            return `${animatedProp} ${formattedDuration} ${easing} ${formattedDelay}`;\n        })\n        .join(',');\n}\n", "/**\n * Credit to MUI team @ https://mui.com\n */\nimport * as React from 'react';\nimport TransitionComponent from '../Transition';\nimport useForkRef from '../useForkRef';\nimport getTransitionProps from '../getTransitionProps';\nimport createTransition from '../createTransition';\nimport { defaultEasing, reflow } from '../utils';\nimport { SlideTransitionDirection, TransitionProps } from '../../types';\n\nfunction ownerDocument(node: Node | null | undefined): Document {\n    return (node && node.ownerDocument) || document;\n}\n\nfunction ownerWindow(node: Node | null): Window {\n    const doc = ownerDocument(node);\n    return doc.defaultView || window;\n}\n\n/**\n * Corresponds to 10 frames at 60 Hz.\n * A few bytes payload overhead when lodash/debounce is ~3 kB and debounce ~300 B.\n */\nfunction debounce(func: () => void, wait = 166) {\n    let timeout: ReturnType<typeof setTimeout>;\n    function debounced(...args: any[]) {\n        const later = () => {\n            // @ts-ignore\n            func.apply(this, args);\n        };\n        clearTimeout(timeout);\n        timeout = setTimeout(later, wait);\n    }\n\n    debounced.clear = () => {\n        clearTimeout(timeout);\n    };\n\n    return debounced;\n}\n\n/**\n * Translate the node so it can't be seen on the screen.\n * Later, we're going to translate the node back to its original location with `none`.\n */\nfunction getTranslateValue(\n    direction: SlideTransitionDirection,\n    node: HTMLElement & { fakeTransform?: string }\n): string {\n    const rect = node.getBoundingClientRect();\n    const containerWindow = ownerWindow(node);\n    let transform;\n\n    if (node.fakeTransform) {\n        transform = node.fakeTransform;\n    } else {\n        const computedStyle = containerWindow.getComputedStyle(node);\n        transform = computedStyle.getPropertyValue('-webkit-transform') || computedStyle.getPropertyValue('transform');\n    }\n\n    let offsetX = 0;\n    let offsetY = 0;\n\n    if (transform && transform !== 'none' && typeof transform === 'string') {\n        const transformValues = transform.split('(')[1].split(')')[0].split(',');\n        offsetX = parseInt(transformValues[4], 10);\n        offsetY = parseInt(transformValues[5], 10);\n    }\n\n    switch (direction) {\n        case 'left':\n            return `translateX(${containerWindow.innerWidth + offsetX - rect.left}px)`;\n        case 'right':\n            return `translateX(-${rect.left + rect.width - offsetX}px)`;\n        case 'up':\n            return `translateY(${containerWindow.innerHeight + offsetY - rect.top}px)`;\n        default:\n            // down\n            return `translateY(-${rect.top + rect.height - offsetY}px)`;\n    }\n}\n\nfunction setTranslateValue(direction: SlideTransitionDirection, node: HTMLElement | null): void {\n    if (!node) return;\n    const transform = getTranslateValue(direction, node);\n    if (transform) {\n        node.style.webkitTransform = transform;\n        node.style.transform = transform;\n    }\n}\n\nconst Slide = React.forwardRef<unknown, TransitionProps>((props, ref) => {\n    const {\n        children,\n        direction = 'down',\n        in: inProp,\n        style,\n        timeout = 0,\n        onEnter,\n        onEntered,\n        onExit,\n        onExited,\n        ...other\n    } = props;\n\n    const nodeRef = React.useRef(null);\n    const handleRefIntermediary = useForkRef((children as any).ref, nodeRef);\n    const handleRef = useForkRef(handleRefIntermediary, ref);\n\n    const handleEnter: TransitionProps['onEnter'] = (node, isAppearing) => {\n        setTranslateValue(direction, node);\n        reflow(node);\n\n        if (onEnter) {\n            onEnter(node, isAppearing);\n        }\n    };\n\n    const handleEntering = (node: HTMLElement) => {\n        const easing = style?.transitionTimingFunction || defaultEasing.easeOut;\n        const transitionProps = getTransitionProps({\n            timeout,\n            mode: 'enter',\n            style: { ...style, transitionTimingFunction: easing },\n        });\n\n        node.style.webkitTransition = createTransition('-webkit-transform', transitionProps);\n        node.style.transition = createTransition('transform', transitionProps);\n\n        node.style.webkitTransform = 'none';\n        node.style.transform = 'none';\n    };\n\n    const handleExit: TransitionProps['onExit'] = (node) => {\n        const easing = style?.transitionTimingFunction || defaultEasing.sharp;\n        const transitionProps = getTransitionProps({\n            timeout,\n            mode: 'exit',\n            style: { ...style, transitionTimingFunction: easing },\n        });\n\n        node.style.webkitTransition = createTransition('-webkit-transform', transitionProps);\n        node.style.transition = createTransition('transform', transitionProps);\n\n        setTranslateValue(direction, node);\n\n        if (onExit) {\n            onExit(node);\n        }\n    };\n\n    const handleExited: TransitionProps['onExited'] = (node) => {\n        // No need for transitions when the component is hidden\n        node.style.webkitTransition = '';\n        node.style.transition = '';\n\n        if (onExited) {\n            onExited(node);\n        }\n    };\n\n    const updatePosition = React.useCallback(() => {\n        if (nodeRef.current) {\n            setTranslateValue(direction, nodeRef.current);\n        }\n    }, [direction]);\n\n    React.useEffect(() => {\n        // Skip configuration where the position is screen size invariant.\n        if (inProp || direction === 'down' || direction === 'right') {\n            return undefined;\n        }\n\n        const handleResize = debounce(() => {\n            if (nodeRef.current) {\n                setTranslateValue(direction, nodeRef.current);\n            }\n        });\n\n        const containerWindow = ownerWindow(nodeRef.current);\n        containerWindow.addEventListener('resize', handleResize);\n        return () => {\n            handleResize.clear();\n            containerWindow.removeEventListener('resize', handleResize);\n        };\n    }, [direction, inProp]);\n\n    React.useEffect(() => {\n        if (!inProp) {\n            // We need to update the position of the drawer when the direction change and\n            // when it's hidden.\n            updatePosition();\n        }\n    }, [inProp, updatePosition]);\n\n    return (\n        <TransitionComponent\n            appear\n            nodeRef={nodeRef}\n            onEnter={handleEnter}\n            onEntered={onEntered}\n            onEntering={handleEntering}\n            onExit={handleExit}\n            onExited={handleExited}\n            in={inProp}\n            timeout={timeout}\n            {...other}\n        >\n            {(state, childProps) =>\n                React.cloneElement(children as any, {\n                    ref: handleRef,\n                    style: {\n                        visibility: state === 'exited' && !inProp ? 'hidden' : undefined,\n                        ...style,\n                        ...(children as any).props.style,\n                    },\n                    ...childProps,\n                })\n            }\n        </TransitionComponent>\n    );\n});\n\nSlide.displayName = 'Slide';\n\nexport default Slide;\n", "import React from 'react';\n\nconst SvgIcon = (props: { children: JSX.Element }) => (\n    <svg\n        viewBox=\"0 0 24 24\"\n        focusable=\"false\"\n        style={{\n            fontSize: 20,\n            marginInlineEnd: 8,\n            userSelect: 'none',\n            width: '1em',\n            height: '1em',\n            display: 'inline-block',\n            fill: 'currentColor',\n            flexShrink: 0,\n        }}\n        {...props}\n    />\n);\n\nconst CheckIcon: React.FC = () => (\n    <SvgIcon>\n        <path\n            d=\"M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41\n        10.59L10 14.17L17.59 6.58L19 8L10 17Z\"\n        />\n    </SvgIcon>\n);\n\nconst WarningIcon: React.FC = () => (\n    <SvgIcon>\n        <path d=\"M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z\" />\n    </SvgIcon>\n);\n\nconst ErrorIcon: React.FC = () => (\n    <SvgIcon>\n        <path\n            d=\"M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,\n        6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,\n        13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z\"\n        />\n    </SvgIcon>\n);\n\nconst InfoIcon: React.FC = () => (\n    <SvgIcon>\n        <path\n            d=\"M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,\n        0 22,12A10,10 0 0,0 12,2Z\"\n        />\n    </SvgIcon>\n);\n\nconst defaultIconVariants: Record<string, React.ReactNode> = {\n    default: undefined,\n    success: <CheckIcon />,\n    warning: <WarningIcon />,\n    error: <ErrorIcon />,\n    info: <InfoIcon />,\n};\n\nexport default defaultIconVariants;\n", "import Slide from '../transitions/Slide';\nimport defaultIconVariants from '../utils/defaultIconVariants';\nimport { InternalSnack } from '../types';\n\nexport const defaults = {\n    maxSnack: 3,\n    persist: false,\n    hideIconVariant: false,\n    disableWindowBlurListener: false,\n    variant: 'default',\n    autoHideDuration: 5000,\n    iconVariant: defaultIconVariants,\n    anchorOrigin: { vertical: 'bottom', horizontal: 'left' },\n    TransitionComponent: Slide,\n    transitionDuration: {\n        enter: 225,\n        exit: 195,\n    },\n};\n\n/**\n * Derives the right autoHideDuration taking into account the following\n * prority order: 1: Options, 2: Props, 3: default fallback\n */\nconst getAutoHideDuration = (optionsDuration: any, propsDuration: any) => {\n    const isNumberOrNull = (numberish: number | null) => typeof numberish === 'number' || numberish === null;\n\n    if (isNumberOrNull(optionsDuration)) return optionsDuration;\n    if (isNumberOrNull(propsDuration)) return propsDuration;\n    return defaults.autoHideDuration;\n};\n\n/**\n * Derives the right transitionDuration taking into account the following\n * prority order: 1: Options, 2: Props, 3: default fallback\n */\nconst getTransitionDuration = (optionsDuration: any, propsDuration: any) => {\n    const is = (item: any, types: string[]) => types.some((t) => typeof item === t);\n\n    if (is(optionsDuration, ['string', 'number'])) {\n        return optionsDuration;\n    }\n\n    if (is(optionsDuration, ['object'])) {\n        return {\n            ...defaults.transitionDuration,\n            ...(is(propsDuration, ['object']) && propsDuration),\n            ...optionsDuration,\n        };\n    }\n\n    if (is(propsDuration, ['string', 'number'])) {\n        return propsDuration;\n    }\n\n    if (is(propsDuration, ['object'])) {\n        return {\n            ...defaults.transitionDuration,\n            ...propsDuration,\n        };\n    }\n\n    return defaults.transitionDuration;\n};\n\nexport const merge =\n    (options: any, props: any) =>\n    (name: keyof InternalSnack, shouldObjectMerge = false): any => {\n        if (shouldObjectMerge) {\n            return {\n                ...(defaults as any)[name],\n                ...props[name],\n                ...options[name],\n            };\n        }\n\n        if (name === 'autoHideDuration') {\n            return getAutoHideDuration(options.autoHideDuration, props.autoHideDuration);\n        }\n\n        if (name === 'transitionDuration') {\n            return getTransitionDuration(options.transitionDuration, props.transitionDuration);\n        }\n\n        return options[name] || props[name] || (defaults as any)[name];\n    };\n", "import { css, CSSAttribute } from 'goober';\n\nexport function makeStyles<S extends { [key: string]: CSSAttribute }, K extends keyof S>(\n    styles: S\n): { [key in K]: string } {\n    return Object.entries(styles).reduce(\n        (acc, [key, value]) => ({\n            ...acc,\n            [key]: css(value),\n        }),\n        {} as { [key in K]: string }\n    );\n}\n\nexport const ComponentClasses = {\n    SnackbarContainer: 'notistack-SnackbarContainer',\n    Snackbar: 'notistack-Snackbar',\n    CollapseWrapper: 'notistack-CollapseWrapper',\n    MuiContent: 'notistack-MuiContent',\n    MuiContentVariant: (variant: string) => `notistack-MuiContent-${variant}`,\n};\n", "/**\n * Credit to MUI team @ https://mui.com\n */\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { reflow } from '../utils';\nimport TransitionComponent from '../Transition';\nimport useForkRef from '../useForkRef';\nimport { TransitionProps } from '../../types';\nimport getTransitionProps from '../getTransitionProps';\nimport createTransition from '../createTransition';\nimport { ComponentClasses, makeStyles } from '../../utils/styles';\n\nconst classes = makeStyles({\n    root: {\n        height: 0,\n    },\n    entered: {\n        height: 'auto',\n    },\n});\n\nconst collapsedSize = '0px';\nconst timeout = 175;\n\ninterface CollapseProps {\n    children: JSX.Element;\n    in: boolean;\n    onExited: TransitionProps['onExited'];\n}\n\nconst Collapse = React.forwardRef<HTMLDivElement, CollapseProps>((props, ref) => {\n    const { children, in: inProp, onExited } = props;\n\n    const wrapperRef = React.useRef<HTMLDivElement>(null);\n\n    const nodeRef = React.useRef<HTMLDivElement>(null);\n    const handleRef = useForkRef(ref, nodeRef);\n\n    const getWrapperSize = () => (wrapperRef.current ? wrapperRef.current.clientHeight : 0);\n\n    const handleEnter: TransitionProps['onEnter'] = (node) => {\n        node.style.height = collapsedSize;\n    };\n\n    const handleEntering = (node: HTMLElement) => {\n        const wrapperSize = getWrapperSize();\n\n        const { duration: transitionDuration, easing } = getTransitionProps({\n            timeout,\n            mode: 'enter',\n        });\n\n        node.style.transitionDuration =\n            typeof transitionDuration === 'string' ? transitionDuration : `${transitionDuration}ms`;\n\n        node.style.height = `${wrapperSize}px`;\n        node.style.transitionTimingFunction = easing || '';\n    };\n\n    const handleEntered: TransitionProps['onEntered'] = (node) => {\n        node.style.height = 'auto';\n    };\n\n    const handleExit: TransitionProps['onExit'] = (node) => {\n        node.style.height = `${getWrapperSize()}px`;\n    };\n\n    const handleExiting = (node: HTMLElement) => {\n        reflow(node);\n\n        const { duration: transitionDuration, easing } = getTransitionProps({\n            timeout,\n            mode: 'exit',\n        });\n\n        node.style.transitionDuration =\n            typeof transitionDuration === 'string' ? transitionDuration : `${transitionDuration}ms`;\n        node.style.height = collapsedSize;\n        node.style.transitionTimingFunction = easing || '';\n    };\n\n    return (\n        <TransitionComponent\n            in={inProp}\n            unmountOnExit\n            onEnter={handleEnter}\n            onEntered={handleEntered}\n            onEntering={handleEntering}\n            onExit={handleExit}\n            onExited={onExited}\n            onExiting={handleExiting}\n            nodeRef={nodeRef}\n            timeout={timeout}\n        >\n            {(state, childProps) => (\n                <div\n                    ref={handleRef}\n                    className={clsx(classes.root, { [classes.entered]: state === 'entered' })}\n                    style={{\n                        pointerEvents: 'all',\n                        overflow: 'hidden',\n                        minHeight: collapsedSize,\n                        transition: createTransition('height'),\n                        ...(state === 'entered' && {\n                            overflow: 'visible',\n                        }),\n                        ...(state === 'exited' &&\n                            !inProp && {\n                                visibility: 'hidden',\n                            }),\n                    }}\n                    {...childProps}\n                >\n                    <div\n                        ref={wrapperRef}\n                        className={ComponentClasses.CollapseWrapper}\n                        // Hack to get children with a negative margin to not falsify the height computation.\n                        style={{ display: 'flex', width: '100%' }}\n                    >\n                        {children}\n                    </div>\n                </div>\n            )}\n        </TransitionComponent>\n    );\n});\n\nCollapse.displayName = 'Collapse';\n\nexport default Collapse;\n", "import {\n    InternalSnack,\n    SlideTransitionDirection,\n    SnackbarOrigin,\n    SnackbarClassKey,\n    SnackbarProviderProps,\n    ClassNameMap,\n    ContainerClassKey,\n} from '../types';\nimport { originKeyExtractor } from '../utils';\n\nconst direction: Record<string, SlideTransitionDirection> = {\n    right: 'left',\n    left: 'right',\n    bottom: 'up',\n    top: 'down',\n};\n\nexport const getSlideDirection = (anchorOrigin: InternalSnack['anchorOrigin']): SlideTransitionDirection => {\n    if (anchorOrigin.horizontal !== 'center') {\n        return direction[anchorOrigin.horizontal];\n    }\n    return direction[anchorOrigin.vertical];\n};\n\n/** Tranforms classes name */\nexport const toSnackbarAnchorOrigin = (anchorOrigin: SnackbarOrigin): SnackbarClassKey =>\n    `anchorOrigin${originKeyExtractor(anchorOrigin)}` as SnackbarClassKey;\n\n/**\n * Omit SnackbarContainer class keys that are not needed for SnackbarItem\n */\nexport const keepSnackbarClassKeys = (\n    classes: SnackbarProviderProps['classes'] = {}\n): Partial<ClassNameMap<SnackbarClassKey>> => {\n    const containerClasses: Record<ContainerClassKey, true> = {\n        containerRoot: true,\n        containerAnchorOriginTopCenter: true,\n        containerAnchorOriginBottomCenter: true,\n        containerAnchorOriginTopRight: true,\n        containerAnchorOriginBottomRight: true,\n        containerAnchorOriginTopLeft: true,\n        containerAnchorOriginBottomLeft: true,\n    };\n    return (Object.keys(classes) as ContainerClassKey[])\n        .filter((key) => !containerClasses[key])\n        .reduce((obj, key) => ({ ...obj, [key]: classes[key] }), {});\n};\n", "import { Snackbar<PERSON>ey } from 'src/types';\n\nconst noOp = () => {\n    /* */\n};\n\n/**\n * Credit to MUI team @ https://mui.com\n * Safe chained function.\n *\n * Will only create a new function if needed,\n * otherwise will pass back existing functions or null.\n */\nexport default function createChainedFunction<Args extends any[], This>(\n    funcs: Array<((this: This, ...args: Args) => any) | undefined>,\n    snackbarId?: SnackbarKey\n): (this: This, ...args: Args) => void {\n    // @ts-ignore\n    return funcs.reduce((acc, func) => {\n        if (func === null || func === undefined) {\n            return acc;\n        }\n\n        return function chainedFunction(...args) {\n            const argums = [...args] as any;\n            if (snackbarId && argums.indexOf(snackbarId) === -1) {\n                argums.push(snackbarId);\n            }\n            // @ts-ignore\n            acc.apply(this, argums);\n            func.apply(this, argums);\n        };\n    }, noOp);\n}\n", "/**\n * Credit to MUI team @ https://mui.com\n * https://github.com/facebook/react/issues/14099#issuecomment-440013892\n */\nimport * as React from 'react';\n\nconst useEnhancedEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n\nexport default function useEventCallback<Args extends unknown[], Return>(\n    fn: (...args: Args) => Return\n): (...args: Args) => Return {\n    const ref = React.useRef(fn);\n    useEnhancedEffect(() => {\n        ref.current = fn;\n    });\n    return React.useCallback(\n        (...args: Args) =>\n            // @ts-expect-error hide `this`\n            (0, ref.current)(...args),\n        []\n    );\n}\n", "/**\n * Credit to MUI team @ https://mui.com\n */\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport useEventCallback from '../utils/useEventCallback';\nimport { CloseReason, SharedProps, SnackbarKey } from '../types';\nimport { ComponentClasses } from '../utils/styles';\n\ninterface SnackbarProps extends Required<Pick<SharedProps, 'disableWindowBlurListener' | 'onClose'>> {\n    open: boolean;\n    id: SnackbarKey;\n    className: string;\n    children: JSX.Element;\n    autoHideDuration: number | null | undefined;\n    SnackbarProps: SharedProps['SnackbarProps'];\n}\n\nconst Snackbar = React.forwardRef<HTMLDivElement, SnackbarProps>((props, ref) => {\n    const {\n        children,\n        className,\n        autoHideDuration,\n        disableWindowBlurListener = false,\n        onClose,\n        id,\n        open,\n        SnackbarProps = {},\n    } = props;\n\n    const timerAutoHide = React.useRef<ReturnType<typeof setTimeout>>();\n\n    const handleClose = useEventCallback((...args: [null, CloseReason, SnackbarKey]) => {\n        if (onClose) {\n            onClose(...args);\n        }\n    });\n\n    const setAutoHideTimer = useEventCallback((autoHideDurationParam?: number | null) => {\n        if (!onClose || autoHideDurationParam == null) {\n            return;\n        }\n\n        if (timerAutoHide.current) {\n            clearTimeout(timerAutoHide.current);\n        }\n        timerAutoHide.current = setTimeout(() => {\n            handleClose(null, 'timeout', id);\n        }, autoHideDurationParam);\n    });\n\n    React.useEffect(() => {\n        if (open) {\n            setAutoHideTimer(autoHideDuration);\n        }\n\n        return () => {\n            if (timerAutoHide.current) {\n                clearTimeout(timerAutoHide.current);\n            }\n        };\n    }, [open, autoHideDuration, setAutoHideTimer]);\n\n    /**\n     * Pause the timer when the user is interacting with the Snackbar\n     * or when the user hide the window.\n     */\n    const handlePause = () => {\n        if (timerAutoHide.current) {\n            clearTimeout(timerAutoHide.current);\n        }\n    };\n\n    /**\n     * Restart the timer when the user is no longer interacting with the Snackbar\n     * or when the window is shown back.\n     */\n    const handleResume = React.useCallback(() => {\n        if (autoHideDuration != null) {\n            setAutoHideTimer(autoHideDuration * 0.5);\n        }\n    }, [autoHideDuration, setAutoHideTimer]);\n\n    const handleMouseEnter: React.MouseEventHandler<HTMLDivElement> = (event) => {\n        if (SnackbarProps.onMouseEnter) {\n            SnackbarProps.onMouseEnter(event);\n        }\n        handlePause();\n    };\n\n    const handleMouseLeave: React.MouseEventHandler<HTMLDivElement> = (event) => {\n        if (SnackbarProps.onMouseLeave) {\n            SnackbarProps.onMouseLeave(event);\n        }\n        handleResume();\n    };\n\n    React.useEffect(() => {\n        if (!disableWindowBlurListener && open) {\n            window.addEventListener('focus', handleResume);\n            window.addEventListener('blur', handlePause);\n\n            return () => {\n                window.removeEventListener('focus', handleResume);\n                window.removeEventListener('blur', handlePause);\n            };\n        }\n\n        return undefined;\n    }, [disableWindowBlurListener, handleResume, open]);\n\n    return (\n        <div\n            ref={ref}\n            {...SnackbarProps}\n            className={clsx(ComponentClasses.Snackbar, className)}\n            onMouseEnter={handleMouseEnter}\n            onMouseLeave={handleMouseLeave}\n        >\n            {children}\n        </div>\n    );\n});\n\nSnackbar.displayName = 'Snackbar';\n\nexport default Snackbar;\n", "import React, { forwardRef } from 'react';\nimport clsx from 'clsx';\nimport { SnackbarContentProps } from '../types';\nimport { breakpoints } from '../utils';\nimport { makeStyles } from '../utils/styles';\n\nconst classes = makeStyles({\n    root: {\n        display: 'flex',\n        flexWrap: 'wrap',\n        flexGrow: 1,\n        [breakpoints.upSm]: {\n            flexGrow: 'initial',\n            minWidth: '288px',\n        },\n    },\n});\n\nconst SnackbarContent = forwardRef<HTMLDivElement, SnackbarContentProps>(({ className, ...props }, ref) => (\n    <div ref={ref} className={clsx(classes.root, className)} {...props} />\n));\n\nSnackbarContent.displayName = 'SnackbarContent';\n\nexport default SnackbarContent;\n", "import React, { memo, forwardRef } from 'react';\nimport clsx from 'clsx';\nimport SnackbarContent from '../../SnackbarContent';\nimport { CustomContentProps } from '../../types';\nimport { ComponentClasses, makeStyles } from '../../utils/styles';\n\nconst classes = makeStyles({\n    root: {\n        backgroundColor: '#313131', // dark grey\n        fontSize: '0.875rem',\n        lineHeight: 1.43,\n        letterSpacing: '0.01071em',\n        color: '#fff',\n        alignItems: 'center',\n        padding: '6px 16px',\n        borderRadius: '4px',\n        boxShadow:\n            '0px 3px 5px -1px rgba(0,0,0,0.2),0px 6px 10px 0px rgba(0,0,0,0.14),0px 1px 18px 0px rgba(0,0,0,0.12)',\n    },\n    lessPadding: {\n        paddingLeft: `${8 * 2.5}px`,\n    },\n    default: {\n        backgroundColor: '#313131', // dark grey\n    },\n    success: {\n        backgroundColor: '#43a047', // green\n    },\n    error: {\n        backgroundColor: '#d32f2f', // dark red\n    },\n    warning: {\n        backgroundColor: '#ff9800', // amber\n    },\n    info: {\n        backgroundColor: '#2196f3', // nice blue\n    },\n    message: {\n        display: 'flex',\n        alignItems: 'center',\n        padding: '8px 0',\n    },\n    action: {\n        display: 'flex',\n        alignItems: 'center',\n        marginLeft: 'auto',\n        paddingLeft: '16px',\n        marginRight: '-8px',\n    },\n});\n\nconst ariaDescribedby = 'notistack-snackbar';\n\nconst MaterialDesignContent = forwardRef<HTMLDivElement, CustomContentProps>((props, forwardedRef) => {\n    const {\n        id,\n        message,\n        action: componentOrFunctionAction,\n        iconVariant,\n        variant,\n        hideIconVariant,\n        style,\n        className,\n    } = props;\n\n    const icon = iconVariant[variant];\n\n    let action = componentOrFunctionAction;\n    if (typeof action === 'function') {\n        action = action(id);\n    }\n\n    return (\n        <SnackbarContent\n            ref={forwardedRef}\n            role=\"alert\"\n            aria-describedby={ariaDescribedby}\n            style={style}\n            className={clsx(\n                ComponentClasses.MuiContent,\n                ComponentClasses.MuiContentVariant(variant),\n                classes.root,\n                { [classes.lessPadding]: !hideIconVariant && icon },\n                classes[variant],\n                className\n            )}\n        >\n            <div id={ariaDescribedby} className={classes.message}>\n                {!hideIconVariant ? icon : null}\n                {message}\n            </div>\n            {action && <div className={classes.action}>{action}</div>}\n        </SnackbarContent>\n    );\n});\n\nMaterialDesignContent.displayName = 'MaterialDesignContent';\n\nexport default memo(MaterialDesignContent);\n", "import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';\nimport clsx from 'clsx';\nimport Collapse from '../transitions/Collapse';\nimport { getSlideDirection, toSnackbarAnchorOrigin, keepSnackbarClassKeys } from './utils';\nimport {\n    TransitionHandlerProps,\n    SnackbarProviderProps,\n    CustomContentProps,\n    InternalSnack,\n    SharedProps,\n} from '../types';\nimport createChainedFunction from '../utils/createChainedFunction';\nimport Snackbar from './Snackbar';\nimport { makeStyles } from '../utils/styles';\nimport MaterialDesignContent from '../ui/MaterialDesignContent';\n\nconst styles = makeStyles({\n    wrappedRoot: {\n        width: '100%',\n        position: 'relative',\n        transform: 'translateX(0)',\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        minWidth: '288px',\n    },\n});\n\ninterface SnackbarItemProps extends Required<Pick<SnackbarProviderProps, 'onEntered' | 'onExited' | 'onClose'>> {\n    snack: InternalSnack;\n    classes: SnackbarProviderProps['classes'];\n    onEnter: SnackbarProviderProps['onEnter'];\n    onExit: SnackbarProviderProps['onExit'];\n    Component?: React.ComponentType<CustomContentProps>;\n}\n\nconst SnackbarItem: React.FC<SnackbarItemProps> = (props) => {\n    const timeout = useRef<ReturnType<typeof setTimeout>>();\n    const [collapsed, setCollapsed] = useState(true);\n\n    const handleClose: NonNullable<SharedProps['onClose']> = createChainedFunction([\n        props.snack.onClose,\n        props.onClose,\n    ]);\n\n    const handleEntered: TransitionHandlerProps['onEntered'] = () => {\n        if (props.snack.requestClose) {\n            handleClose(null, 'instructed', props.snack.id);\n        }\n    };\n\n    const handleExitedScreen = useCallback((): void => {\n        timeout.current = setTimeout(() => {\n            setCollapsed((col) => !col);\n        }, 125);\n    }, []);\n\n    useEffect(\n        () => (): void => {\n            if (timeout.current) {\n                clearTimeout(timeout.current);\n            }\n        },\n        []\n    );\n\n    const { snack, classes: allClasses, Component = MaterialDesignContent } = props;\n\n    const classes = useMemo(() => keepSnackbarClassKeys(allClasses), [allClasses]);\n\n    const {\n        open,\n        SnackbarProps,\n        TransitionComponent,\n        TransitionProps,\n        transitionDuration,\n        disableWindowBlurListener,\n        content: componentOrFunctionContent,\n        entered: ignoredEntered,\n        requestClose: ignoredRequestClose,\n        onEnter: ignoreOnEnter,\n        onEntered: ignoreOnEntered,\n        onExit: ignoreOnExit,\n        onExited: ignoreOnExited,\n        ...otherSnack\n    } = snack;\n\n    const transitionProps = {\n        direction: getSlideDirection(otherSnack.anchorOrigin),\n        timeout: transitionDuration,\n        ...TransitionProps,\n    };\n\n    let content = componentOrFunctionContent;\n    if (typeof content === 'function') {\n        content = content(otherSnack.id, otherSnack.message);\n    }\n\n    const callbacks: { [key in keyof TransitionHandlerProps]?: any } = (\n        ['onEnter', 'onEntered', 'onExit', 'onExited'] as (keyof TransitionHandlerProps)[]\n    ).reduce(\n        (acc, cbName) => ({\n            ...acc,\n            [cbName]: createChainedFunction([props.snack[cbName] as any, props[cbName] as any], otherSnack.id),\n        }),\n        {}\n    );\n\n    return (\n        <Collapse in={collapsed} onExited={callbacks.onExited}>\n            <Snackbar\n                open={open}\n                id={otherSnack.id}\n                disableWindowBlurListener={disableWindowBlurListener}\n                autoHideDuration={otherSnack.autoHideDuration}\n                className={clsx(\n                    styles.wrappedRoot,\n                    classes.root,\n                    classes[toSnackbarAnchorOrigin(otherSnack.anchorOrigin)]\n                )}\n                SnackbarProps={SnackbarProps}\n                onClose={handleClose}\n            >\n                <TransitionComponent\n                    {...transitionProps}\n                    appear\n                    in={open}\n                    onExit={callbacks.onExit}\n                    onExited={handleExitedScreen}\n                    onEnter={callbacks.onEnter}\n                    // order matters. first callbacks.onEntered to set entered: true,\n                    // then handleEntered to check if there's a request for closing\n                    onEntered={createChainedFunction([callbacks.onEntered, handleEntered], otherSnack.id)}\n                >\n                    {(content as React.ReactElement) || <Component {...otherSnack} />}\n                </TransitionComponent>\n            </Snackbar>\n        </Collapse>\n    );\n};\n\nexport default SnackbarItem;\n", "import React, { memo } from 'react';\nimport clsx from 'clsx';\nimport createTransition from '../transitions/createTransition';\nimport { makeStyles, ComponentClasses } from '../utils/styles';\nimport { breakpoints, originKeyExtractor } from '../utils';\nimport { ContainerClassKey, SnackbarProviderProps } from '../types';\n\nconst indents = {\n    view: { default: 20, dense: 4 },\n    snackbar: { default: 6, dense: 2 },\n};\n\nconst collapseWrapper = `.${ComponentClasses.CollapseWrapper}`;\n\nconst xsWidthMargin = 16;\n\nconst styles = makeStyles({\n    root: {\n        boxSizing: 'border-box',\n        display: 'flex',\n        maxHeight: '100%',\n        position: 'fixed',\n        zIndex: 1400,\n        height: 'auto',\n        width: 'auto',\n        transition: createTransition(['top', 'right', 'bottom', 'left', 'max-width'], {\n            duration: 300,\n            easing: 'ease',\n        }),\n        // container itself is invisible and should not block clicks, clicks should be passed to its children\n        // a pointerEvents: all is applied in the collapse component\n        pointerEvents: 'none',\n        [collapseWrapper]: {\n            padding: `${indents.snackbar.default}px 0px`,\n            transition: 'padding 300ms ease 0ms',\n        },\n        maxWidth: `calc(100% - ${indents.view.default * 2}px)`,\n        [breakpoints.downXs]: {\n            width: '100%',\n            maxWidth: `calc(100% - ${xsWidthMargin * 2}px)`,\n        },\n    },\n    rootDense: {\n        [collapseWrapper]: {\n            padding: `${indents.snackbar.dense}px 0px`,\n        },\n    },\n    top: {\n        top: `${indents.view.default - indents.snackbar.default}px`,\n        flexDirection: 'column',\n    },\n    bottom: {\n        bottom: `${indents.view.default - indents.snackbar.default}px`,\n        flexDirection: 'column-reverse',\n    },\n    left: {\n        left: `${indents.view.default}px`,\n        [breakpoints.upSm]: {\n            alignItems: 'flex-start',\n        },\n        [breakpoints.downXs]: {\n            left: `${xsWidthMargin}px`,\n        },\n    },\n    right: {\n        right: `${indents.view.default}px`,\n        [breakpoints.upSm]: {\n            alignItems: 'flex-end',\n        },\n        [breakpoints.downXs]: {\n            right: `${xsWidthMargin}px`,\n        },\n    },\n    center: {\n        left: '50%',\n        transform: 'translateX(-50%)',\n        [breakpoints.upSm]: {\n            alignItems: 'center',\n        },\n    },\n});\n\ninterface SnackbarContainerProps {\n    children: React.ReactNode;\n    dense: SnackbarProviderProps['dense'];\n    anchorOrigin: NonNullable<SnackbarProviderProps['anchorOrigin']>;\n    classes: SnackbarProviderProps['classes'];\n}\n\nconst SnackbarContainer: React.FC<SnackbarContainerProps> = (props) => {\n    const { classes = {}, anchorOrigin, dense, children } = props;\n\n    const combinedClassname = clsx(\n        ComponentClasses.SnackbarContainer,\n        styles[anchorOrigin.vertical],\n        styles[anchorOrigin.horizontal],\n        { [styles.rootDense]: dense },\n        styles.root, // root should come after others to override maxWidth\n        classes.containerRoot,\n        classes[`containerAnchorOrigin${originKeyExtractor(anchorOrigin)}` as ContainerClassKey]\n    );\n\n    return <div className={combinedClassname}>{children}</div>;\n};\n\nexport default memo(SnackbarContainer);\n", "/* eslint-disable */\nconst __DEV__ = process.env.NODE_ENV !== 'production';\n\nconst messages = {\n    NO_PERSIST_ALL:\n        \"Reached maxSnack while all enqueued snackbars have 'persist' flag. Notistack will dismiss the oldest snackbar anyway to allow other ones in the queue to be presented.\",\n};\n\nexport default (messageKey: keyof typeof messages): void => {\n    if (!__DEV__) return;\n\n    const message = messages[messageKey];\n    if (typeof console !== 'undefined') {\n        console.error(`WARNING - notistack: ${message}`);\n    }\n    try {\n        throw new Error(message);\n    } catch (x) {}\n};\n", "import React, { Component, isValidElement } from 'react';\nimport { createPortal } from 'react-dom';\nimport clsx from 'clsx';\nimport SnackbarContext from '../SnackbarContext';\nimport { originKeyExtractor, isDefined } from '../utils';\nimport { defaults, merge } from './merger';\nimport SnackbarItem from '../SnackbarItem';\nimport SnackbarContainer from '../SnackbarContainer';\nimport warning from '../utils/warning';\nimport {\n    SnackbarProviderProps,\n    SnackbarKey,\n    ProviderContext,\n    TransitionHandlerProps,\n    InternalSnack,\n    OptionsObject,\n    SharedProps,\n    SnackbarMessage,\n} from '../types';\nimport createChainedFunction from '../utils/createChainedFunction';\n\nconst isOptions = (\n    messageOrOptions: SnackbarMessage | (OptionsObject & { message?: SnackbarMessage })\n): messageOrOptions is OptionsObject & { message?: SnackbarMessage } => {\n    const isMessage = typeof messageOrOptions === 'string' || isValidElement(messageOrOptions);\n    return !isMessage;\n};\n\ntype Reducer = (state: State) => State;\ntype SnacksByPosition = { [key: string]: InternalSnack[] };\n\ninterface State {\n    snacks: InternalSnack[];\n    queue: InternalSnack[];\n    contextValue: ProviderContext;\n}\n\nexport let enqueueSnackbar: ProviderContext['enqueueSnackbar'];\nexport let closeSnackbar: ProviderContext['closeSnackbar'];\n\nclass SnackbarProvider extends Component<SnackbarProviderProps, State> {\n    constructor(props: SnackbarProviderProps) {\n        super(props);\n        enqueueSnackbar = this.enqueueSnackbar;\n        closeSnackbar = this.closeSnackbar;\n\n        this.state = {\n            snacks: [],\n            queue: [],\n            contextValue: {\n                enqueueSnackbar: this.enqueueSnackbar.bind(this),\n                closeSnackbar: this.closeSnackbar.bind(this),\n            },\n        };\n    }\n\n    get maxSnack(): number {\n        return this.props.maxSnack || defaults.maxSnack;\n    }\n\n    /**\n     * Adds a new snackbar to the queue to be presented.\n     * Returns generated or user defined key referencing the new snackbar or null\n     */\n    enqueueSnackbar = (\n        messageOrOptions: SnackbarMessage | (OptionsObject & { message?: SnackbarMessage }),\n        optsOrUndefined: OptionsObject = {}\n    ): SnackbarKey => {\n        if (messageOrOptions === undefined || messageOrOptions === null) {\n            throw new Error('enqueueSnackbar called with invalid argument');\n        }\n\n        const opts = isOptions(messageOrOptions) ? messageOrOptions : optsOrUndefined;\n\n        const message: SnackbarMessage | undefined = isOptions(messageOrOptions)\n            ? messageOrOptions.message\n            : messageOrOptions;\n\n        const { key, preventDuplicate, ...options } = opts;\n\n        const hasSpecifiedKey = isDefined(key);\n        const id = hasSpecifiedKey ? (key as SnackbarKey) : new Date().getTime() + Math.random();\n\n        const merger = merge(options, this.props);\n        const snack: InternalSnack = {\n            id,\n            ...options,\n            message,\n            open: true,\n            entered: false,\n            requestClose: false,\n            persist: merger('persist'),\n            action: merger('action'),\n            content: merger('content'),\n            variant: merger('variant'),\n            anchorOrigin: merger('anchorOrigin'),\n            disableWindowBlurListener: merger('disableWindowBlurListener'),\n            autoHideDuration: merger('autoHideDuration'),\n            hideIconVariant: merger('hideIconVariant'),\n            TransitionComponent: merger('TransitionComponent'),\n            transitionDuration: merger('transitionDuration'),\n            TransitionProps: merger('TransitionProps', true),\n            iconVariant: merger('iconVariant', true),\n            style: merger('style', true),\n            SnackbarProps: merger('SnackbarProps', true),\n            className: clsx(this.props.className, options.className),\n        };\n\n        if (snack.persist) {\n            snack.autoHideDuration = undefined;\n        }\n\n        this.setState((state) => {\n            if ((preventDuplicate === undefined && this.props.preventDuplicate) || preventDuplicate) {\n                const compareFunction = (item: InternalSnack): boolean =>\n                    hasSpecifiedKey ? item.id === id : item.message === message;\n\n                const inQueue = state.queue.findIndex(compareFunction) > -1;\n                const inView = state.snacks.findIndex(compareFunction) > -1;\n                if (inQueue || inView) {\n                    return state;\n                }\n            }\n\n            return this.handleDisplaySnack({\n                ...state,\n                queue: [...state.queue, snack],\n            });\n        });\n\n        return id;\n    };\n\n    /**\n     * Reducer: Display snack if there's space for it. Otherwise, immediately\n     * begin dismissing the oldest message to start showing the new one.\n     */\n    handleDisplaySnack: Reducer = (state) => {\n        const { snacks } = state;\n        if (snacks.length >= this.maxSnack) {\n            return this.handleDismissOldest(state);\n        }\n        return this.processQueue(state);\n    };\n\n    /**\n     * Reducer: Display items (notifications) in the queue if there's space for them.\n     */\n    processQueue: Reducer = (state) => {\n        const { queue, snacks } = state;\n        if (queue.length > 0) {\n            return {\n                ...state,\n                snacks: [...snacks, queue[0]],\n                queue: queue.slice(1, queue.length),\n            };\n        }\n        return state;\n    };\n\n    /**\n     * Reducer: Hide oldest snackbar on the screen because there exists a new one which we have to display.\n     * (ignoring the one with 'persist' flag. i.e. explicitly told by user not to get dismissed).\n     *\n     * Note 1: If there is already a message leaving the screen, no new messages are dismissed.\n     * Note 2: If the oldest message has not yet entered the screen, only a request to close the\n     *         snackbar is made. Once it entered the screen, it will be immediately dismissed.\n     */\n    handleDismissOldest: Reducer = (state) => {\n        if (state.snacks.some((item) => !item.open || item.requestClose)) {\n            return state;\n        }\n\n        let popped = false;\n        let ignore = false;\n\n        const persistentCount = state.snacks.reduce(\n            (acc, current) => acc + (current.open && current.persist ? 1 : 0),\n            0\n        );\n\n        if (persistentCount === this.maxSnack) {\n            warning('NO_PERSIST_ALL');\n            ignore = true;\n        }\n\n        const snacks = state.snacks.map((item) => {\n            if (!popped && (!item.persist || ignore)) {\n                popped = true;\n\n                if (!item.entered) {\n                    return {\n                        ...item,\n                        requestClose: true,\n                    };\n                }\n\n                if (item.onClose) {\n                    item.onClose(null, 'maxsnack', item.id);\n                }\n\n                if (this.props.onClose) {\n                    this.props.onClose(null, 'maxsnack', item.id);\n                }\n\n                return {\n                    ...item,\n                    open: false,\n                };\n            }\n\n            return { ...item };\n        });\n\n        return { ...state, snacks };\n    };\n\n    /**\n     * Set the entered state of the snackbar with the given key.\n     */\n    handleEnteredSnack: TransitionHandlerProps['onEntered'] = (node, isAppearing, key) => {\n        if (!isDefined(key)) {\n            throw new Error('handleEnteredSnack Cannot be called with undefined key');\n        }\n\n        this.setState(({ snacks }) => ({\n            snacks: snacks.map((item) => (item.id === key ? { ...item, entered: true } : { ...item })),\n        }));\n    };\n\n    /**\n     * Hide a snackbar after its timeout.\n     */\n    handleCloseSnack: NonNullable<SharedProps['onClose']> = (event, reason, key) => {\n        // should not use createChainedFunction for onClose.\n        // because this.closeSnackbar called this function\n        if (this.props.onClose) {\n            this.props.onClose(event, reason, key);\n        }\n\n        const shouldCloseAll = key === undefined;\n\n        this.setState(({ snacks, queue }) => ({\n            snacks: snacks.map((item) => {\n                if (!shouldCloseAll && item.id !== key) {\n                    return { ...item };\n                }\n\n                return item.entered ? { ...item, open: false } : { ...item, requestClose: true };\n            }),\n            queue: queue.filter((item) => item.id !== key),\n        }));\n    };\n\n    /**\n     * Close snackbar with the given key\n     */\n    closeSnackbar: ProviderContext['closeSnackbar'] = (key) => {\n        // call individual snackbar onClose callback passed through options parameter\n        const toBeClosed = this.state.snacks.find((item) => item.id === key);\n        if (isDefined(key) && toBeClosed && toBeClosed.onClose) {\n            toBeClosed.onClose(null, 'instructed', key);\n        }\n\n        this.handleCloseSnack(null, 'instructed', key);\n    };\n\n    /**\n     * When we set open attribute of a snackbar to false (i.e. after we hide a snackbar),\n     * it leaves the screen and immediately after leaving animation is done, this method\n     * gets called. We remove the hidden snackbar from state and then display notifications\n     * waiting in the queue (if any). If after this process the queue is not empty, the\n     * oldest message is dismissed.\n     */\n    handleExitedSnack: TransitionHandlerProps['onExited'] = (node, key) => {\n        if (!isDefined(key)) {\n            throw new Error('handleExitedSnack Cannot be called with undefined key');\n        }\n\n        this.setState((state) => {\n            const newState = this.processQueue({\n                ...state,\n                snacks: state.snacks.filter((item) => item.id !== key),\n            });\n\n            if (newState.queue.length === 0) {\n                return newState;\n            }\n\n            return this.handleDismissOldest(newState);\n        });\n    };\n\n    render(): JSX.Element {\n        const { contextValue } = this.state;\n        const { domRoot, children, dense = false, Components = {}, classes } = this.props;\n\n        const categ = this.state.snacks.reduce<SnacksByPosition>((acc, current) => {\n            const category = originKeyExtractor(current.anchorOrigin);\n            const existingOfCategory = acc[category] || [];\n            return {\n                ...acc,\n                [category]: [...existingOfCategory, current],\n            };\n        }, {});\n\n        const snackbars = Object.keys(categ).map((origin) => {\n            const snacks = categ[origin];\n            const [nomineeSnack] = snacks;\n            return (\n                <SnackbarContainer\n                    key={origin}\n                    dense={dense}\n                    anchorOrigin={nomineeSnack.anchorOrigin}\n                    classes={classes}\n                >\n                    {snacks.map((snack) => (\n                        <SnackbarItem\n                            key={snack.id}\n                            snack={snack}\n                            classes={classes}\n                            Component={Components[snack.variant]}\n                            onClose={this.handleCloseSnack}\n                            onEnter={this.props.onEnter}\n                            onExit={this.props.onExit}\n                            onExited={createChainedFunction([this.handleExitedSnack, this.props.onExited], snack.id)}\n                            onEntered={createChainedFunction([this.handleEnteredSnack, this.props.onEntered], snack.id)}\n                        />\n                    ))}\n                </SnackbarContainer>\n            );\n        });\n\n        return (\n            <SnackbarContext.Provider value={contextValue}>\n                {children}\n                {domRoot ? createPortal(snackbars, domRoot) : snackbars}\n            </SnackbarContext.Provider>\n        );\n    }\n}\n\nexport default SnackbarProvider;\n", "import { useContext } from 'react';\nimport SnackbarContext from './SnackbarContext';\nimport { ProviderContext } from './types';\n\nexport default (): ProviderContext => useContext(SnackbarContext);\n"], "names": ["noOp", "React", "createContext", "enqueueSnackbar", "closeSnackbar", "breakpoints", "downXs", "upSm", "capitalise", "text", "char<PERSON>t", "toUpperCase", "slice", "originKeyExtractor", "anchor", "vertical", "horizontal", "isDefined", "value", "UNMOUNTED", "EXITED", "ENTERING", "ENTERED", "EXITING", "Transition", "props", "appear", "initialStatus", "appearStatus", "unmountOnExit", "mountOnEnter", "state", "status", "nextCallback", "getDerivedStateFromProps", "prevState", "nextIn", "componentDidMount", "updateStatus", "componentDidUpdate", "prevProps", "nextStatus", "componentWillUnmount", "cancelNextCallback", "getTimeouts", "timeout", "enter", "exit", "mounting", "performEnter", "performExit", "setState", "isAppearing", "timeouts", "safeSetState", "onEntered", "node", "onEnter", "onEntering", "onTransitionEnd", "onExited", "onExit", "onExiting", "cancel", "nextState", "callback", "setNextCallback", "active", "handler", "doesNotHaveTimeoutOrListener", "addEndListener", "setTimeout", "render", "children", "_in", "childProps", "nodeRef", "current", "Error", "Component", "noop", "defaultProps", "setRef", "ref", "useForkRef", "refA", "refB", "refValue", "getTransitionProps", "style", "mode", "duration", "easing", "transitionTimingFunction", "delay", "transitionDelay", "defaultEasing", "easeInOut", "easeOut", "easeIn", "sharp", "reflow", "scrollTop", "formatMs", "milliseconds", "Math", "round", "createTransition", "options", "properties", "Array", "isArray", "map", "animatedProp", "formattedDuration", "formattedDelay", "join", "ownerDocument", "document", "ownerWindow", "doc", "defaultView", "window", "debounce", "func", "wait", "debounced", "args", "later", "apply", "clearTimeout", "clear", "getTranslateValue", "direction", "rect", "getBoundingClientRect", "containerWindow", "transform", "fakeTransform", "computedStyle", "getComputedStyle", "getPropertyValue", "offsetX", "offsetY", "transformValues", "split", "parseInt", "innerWidth", "left", "width", "innerHeight", "top", "height", "setTranslateValue", "webkitTransform", "Slide", "inProp", "other", "handleRefIntermediary", "handleRef", "handleEnter", "handleEntering", "transitionProps", "webkitTransition", "transition", "handleExit", "handleExited", "updatePosition", "undefined", "handleResize", "addEventListener", "removeEventListener", "TransitionComponent", "visibility", "displayName", "SvgIcon", "viewBox", "focusable", "fontSize", "marginInlineEnd", "userSelect", "display", "fill", "flexShrink", "CheckIcon", "d", "WarningIcon", "ErrorIcon", "InfoIcon", "defaultIconVariants", "success", "warning", "error", "info", "defaults", "maxSnack", "persist", "hideIconVariant", "disableWindowBlurListener", "variant", "autoHideDuration", "icon<PERSON><PERSON><PERSON>", "anchor<PERSON><PERSON><PERSON>", "transitionDuration", "getAutoHideDuration", "optionsDuration", "propsDuration", "isNumberOrNull", "numberish", "getTransitionDuration", "is", "item", "types", "some", "t", "merge", "name", "shouldObjectMerge", "makeStyles", "styles", "Object", "entries", "reduce", "acc", "key", "css", "ComponentClasses", "SnackbarContainer", "Snackbar", "CollapseWrapper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Mui<PERSON><PERSON>nt<PERSON><PERSON><PERSON>", "classes", "root", "entered", "collapsedSize", "Collapse", "wrapperRef", "getWrapperSize", "clientHeight", "wrapperSize", "handleEntered", "handleExiting", "className", "clsx", "pointerEvents", "overflow", "minHeight", "right", "bottom", "getSlideDirection", "toSnackbarAnchorOrigin", "keepSnackbarClassKeys", "containerClasses", "containerRoot", "containerAnchorOriginTopCenter", "containerAnchorOriginBottomCenter", "containerAnchorOriginTopRight", "containerAnchorOriginBottomRight", "containerAnchorOriginTopLeft", "containerAnchorOriginBottomLeft", "keys", "filter", "obj", "createChainedFunction", "funcs", "snackbarId", "chainedFunction", "argums", "indexOf", "push", "useEnhancedEffect", "useEventCallback", "fn", "onClose", "id", "open", "SnackbarProps", "timerAutoHide", "handleClose", "setAutoHideTimer", "autoHideDurationParam", "handlePause", "handleResume", "handleMouseEnter", "event", "onMouseEnter", "handleMouseLeave", "onMouseLeave", "flexWrap", "flexGrow", "min<PERSON><PERSON><PERSON>", "SnackbarContent", "forwardRef", "backgroundColor", "lineHeight", "letterSpacing", "color", "alignItems", "padding", "borderRadius", "boxShadow", "lessPadding", "paddingLeft", "message", "action", "marginLeft", "marginRight", "aria<PERSON><PERSON><PERSON><PERSON>", "MaterialDesignContent", "forwardedRef", "componentOrFunctionAction", "icon", "role", "memo", "wrappedRoot", "position", "SnackbarItem", "useRef", "useState", "collapsed", "setCollapsed", "snack", "requestClose", "handleExitedScreen", "useCallback", "col", "useEffect", "allClasses", "useMemo", "TransitionProps", "componentOrFunctionContent", "content", "ignoredEntered", "otherSnack", "callbacks", "cbName", "indents", "view", "dense", "snackbar", "collapseWrapper", "xsWidthMargin", "boxSizing", "maxHeight", "zIndex", "max<PERSON><PERSON><PERSON>", "rootDense", "flexDirection", "center", "combinedClassname", "__DEV__", "process", "env", "NODE_ENV", "messages", "NO_PERSIST_ALL", "message<PERSON>ey", "console", "x", "isOptions", "messageOrOptions", "isMessage", "isValidElement", "SnackbarProvider", "optsOrUndefined", "opts", "preventDuplicate", "hasSpecifiedKey", "Date", "getTime", "random", "merger", "compareFunction", "inQueue", "queue", "findIndex", "inView", "snacks", "handleDisplaySnack", "length", "handleDismissOldest", "processQueue", "popped", "ignore", "persistentCount", "reason", "shouldCloseAll", "toBeClosed", "find", "handleCloseSnack", "newState", "contextValue", "bind", "domRoot", "Components", "categ", "category", "existingOfCategory", "snackbars", "origin", "nomineeSnack", "handleExitedSnack", "handleEnteredSnack", "SnackbarContext", "Provider", "createPortal", "useContext"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAMA,IAAI,GAAG,SAAPA,IAAO;AACT,SAAO,EAAP;AACH,CAFD;;AAIA,mCAAeC,cAAK,CAACC,aAAN,CAAqC;AAChDC,EAAAA,eAAe,EAAEH,IAD+B;AAEhDI,EAAAA,aAAa,EAAEJ;AAFiC,CAArC,CAAf;;ACLO,IAAMK,WAAW,GAAG;AACvBC,EAAAA,MAAM,EAAE,6BADe;AAEvBC,EAAAA,IAAI,EAAE;AAFiB,CAApB;;AAKP,IAAMC,UAAU,GAAG,SAAbA,UAAa,CAACC,IAAD;AAAA,SAA0BA,IAAI,CAACC,MAAL,CAAY,CAAZ,EAAeC,WAAf,KAA+BF,IAAI,CAACG,KAAL,CAAW,CAAX,CAAzD;AAAA,CAAnB;;AAEA,AAAO,IAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,CAACC,MAAD;AAAA,cAC3BN,UAAU,CAACM,MAAM,CAACC,QAAR,CADiB,GACGP,UAAU,CAACM,MAAM,CAACE,UAAR,CADb;AAAA,CAA3B;AAGP,AAAO,IAAMC,SAAS,GAAG,SAAZA,SAAY,CAACC,KAAD;AAAA,SAAwD,CAAC,CAACA,KAAF,IAAWA,KAAK,KAAK,CAA7E;AAAA,CAAlB;;ACuBP,IAAMC,SAAS,GAAG,WAAlB;AACA,IAAMC,MAAM,GAAG,QAAf;AACA,IAAMC,QAAQ,GAAG,UAAjB;AACA,IAAMC,OAAO,GAAG,SAAhB;AACA,IAAMC,OAAO,GAAG,SAAhB;;IAWMC;;;AAKF,sBAAYC,KAAZ;;;AACI,wCAAMA,KAAN;QAEQC,SAAWD,MAAXC;AAER,QAAIC,aAAJ;AAEA,UAAKC,YAAL,GAAoB,IAApB;;AAEA,QAAIH,KAAK,MAAT,EAAc;AACV,UAAIC,MAAJ,EAAY;AACRC,QAAAA,aAAa,GAAGP,MAAhB;AACA,cAAKQ,YAAL,GAAoBP,QAApB;AACH,OAHD,MAGO;AACHM,QAAAA,aAAa,GAAGL,OAAhB;AACH;AACJ,KAPD,MAOO,IAAIG,KAAK,CAACI,aAAN,IAAuBJ,KAAK,CAACK,YAAjC,EAA+C;AAClDH,MAAAA,aAAa,GAAGR,SAAhB;AACH,KAFM,MAEA;AACHQ,MAAAA,aAAa,GAAGP,MAAhB;AACH;;AAED,UAAKW,KAAL,GAAa;AAAEC,MAAAA,MAAM,EAAEL;AAAV,KAAb;AAEA,UAAKM,YAAL,GAAoB,IAApB;;AACH;;aAEMC,2BAAP,wCAA0EC,SAA1E;QAAsCC;;AAClC,QAAIA,MAAM,IAAID,SAAS,CAACH,MAAV,KAAqBb,SAAnC,EAA8C;AAC1C,aAAO;AAAEa,QAAAA,MAAM,EAAEZ;AAAV,OAAP;AACH;;AACD,WAAO,IAAP;AACH;;;;SAEDiB,oBAAA;AACI,SAAKC,YAAL,CAAkB,IAAlB,EAAwB,KAAKV,YAA7B;AACH;;SAEDW,qBAAA,4BAAmBC,SAAnB;AACI,QAAIC,UAAU,GAA4B,IAA1C;;AACA,QAAID,SAAS,KAAK,KAAKf,KAAvB,EAA8B;AAAA,UAClBO,MADkB,GACP,KAAKD,KADE,CAClBC,MADkB;;AAG1B,UAAI,KAAKP,KAAL,MAAJ,EAAmB;AACf,YAAIO,MAAM,KAAKX,QAAX,IAAuBW,MAAM,KAAKV,OAAtC,EAA+C;AAC3CmB,UAAAA,UAAU,GAAGpB,QAAb;AACH;AACJ,OAJD,MAIO,IAAIW,MAAM,KAAKX,QAAX,IAAuBW,MAAM,KAAKV,OAAtC,EAA+C;AAClDmB,QAAAA,UAAU,GAAGlB,OAAb;AACH;AACJ;;AACD,SAAKe,YAAL,CAAkB,KAAlB,EAAyBG,UAAzB;AACH;;SAEDC,uBAAA;AACI,SAAKC,kBAAL;AACH;;SAEDC,cAAA;QACYC,UAAY,KAAKpB,MAAjBoB;AACR,QAAIC,KAAK,GAAGD,OAAZ;AACA,QAAIE,IAAI,GAAGF,OAAX;;AAEA,QAAIA,OAAO,IAAI,IAAX,IAAmB,OAAOA,OAAP,KAAmB,QAAtC,IAAkD,OAAOA,OAAP,KAAmB,QAAzE,EAAmF;AAC/EE,MAAAA,IAAI,GAAGF,OAAO,CAACE,IAAf;AACAD,MAAAA,KAAK,GAAGD,OAAO,CAACC,KAAhB;AACH;;AACD,WAAO;AACHC,MAAAA,IAAI,EAAEA,IADH;AAEHD,MAAAA,KAAK,EAAEA;AAFJ,KAAP;AAIH;;SAEDR,eAAA,sBAAaU,QAAb,EAA+BP,UAA/B;QAAaO;AAAAA,MAAAA,WAAW;;;AACpB,QAAIP,UAAU,KAAK,IAAnB,EAAyB;AACrB,WAAKE,kBAAL;;AAEA,UAAIF,UAAU,KAAKpB,QAAnB,EAA6B;AACzB,aAAK4B,YAAL,CAAkBD,QAAlB;AACH,OAFD,MAEO;AACH,aAAKE,WAAL;AACH;AACJ,KARD,MAQO,IAAI,KAAKzB,KAAL,CAAWI,aAAX,IAA4B,KAAKE,KAAL,CAAWC,MAAX,KAAsBZ,MAAtD,EAA8D;AACjE,WAAK+B,QAAL,CAAc;AAAEnB,QAAAA,MAAM,EAAEb;AAAV,OAAd;AACH;AACJ;;SAUD8B,eAAA,sBAAaD,QAAb;;;QACYF,QAAU,KAAKrB,MAAfqB;AACR,QAAMM,WAAW,GAAGJ,QAApB;AAEA,QAAMK,QAAQ,GAAG,KAAKT,WAAL,EAAjB;;AAEA,QAAI,CAACI,QAAD,IAAa,CAACF,KAAlB,EAAyB;AACrB,WAAKQ,YAAL,CAAkB;AAAEtB,QAAAA,MAAM,EAAEV;AAAV,OAAlB,EAAuC;AACnC,YAAI,MAAI,CAACG,KAAL,CAAW8B,SAAf,EAA0B;AACtB,UAAA,MAAI,CAAC9B,KAAL,CAAW8B,SAAX,CAAqB,MAAI,CAACC,IAA1B,EAAgCJ,WAAhC;AACH;AACJ,OAJD;AAKA;AACH;;AAED,QAAI,KAAK3B,KAAL,CAAWgC,OAAf,EAAwB;AACpB,WAAKhC,KAAL,CAAWgC,OAAX,CAAmB,KAAKD,IAAxB,EAA8BJ,WAA9B;AACH;;AAED,SAAKE,YAAL,CAAkB;AAAEtB,MAAAA,MAAM,EAAEX;AAAV,KAAlB,EAAwC;AACpC,UAAI,MAAI,CAACI,KAAL,CAAWiC,UAAf,EAA2B;AACvB,QAAA,MAAI,CAACjC,KAAL,CAAWiC,UAAX,CAAsB,MAAI,CAACF,IAA3B,EAAiCJ,WAAjC;AACH;;AAED,MAAA,MAAI,CAACO,eAAL,CAAqBN,QAAQ,CAACP,KAA9B,EAAqC;AACjC,QAAA,MAAI,CAACQ,YAAL,CAAkB;AAAEtB,UAAAA,MAAM,EAAEV;AAAV,SAAlB,EAAuC;AACnC,cAAI,MAAI,CAACG,KAAL,CAAW8B,SAAf,EAA0B;AACtB,YAAA,MAAI,CAAC9B,KAAL,CAAW8B,SAAX,CAAqB,MAAI,CAACC,IAA1B,EAAgCJ,WAAhC;AACH;AACJ,SAJD;AAKH,OAND;AAOH,KAZD;AAaH;;SAEDF,cAAA;;;QACYH,OAAS,KAAKtB,MAAdsB;AACR,QAAMM,QAAQ,GAAG,KAAKT,WAAL,EAAjB;;AAGA,QAAI,CAACG,IAAL,EAAW;AACP,WAAKO,YAAL,CAAkB;AAAEtB,QAAAA,MAAM,EAAEZ;AAAV,OAAlB,EAAsC;AAClC,YAAI,MAAI,CAACK,KAAL,CAAWmC,QAAf,EAAyB;AACrB,UAAA,MAAI,CAACnC,KAAL,CAAWmC,QAAX,CAAoB,MAAI,CAACJ,IAAzB;AACH;AACJ,OAJD;AAKA;AACH;;AAED,QAAI,KAAK/B,KAAL,CAAWoC,MAAf,EAAuB;AACnB,WAAKpC,KAAL,CAAWoC,MAAX,CAAkB,KAAKL,IAAvB;AACH;;AAED,SAAKF,YAAL,CAAkB;AAAEtB,MAAAA,MAAM,EAAET;AAAV,KAAlB,EAAuC;AACnC,UAAI,MAAI,CAACE,KAAL,CAAWqC,SAAf,EAA0B;AACtB,QAAA,MAAI,CAACrC,KAAL,CAAWqC,SAAX,CAAqB,MAAI,CAACN,IAA1B;AACH;;AAED,MAAA,MAAI,CAACG,eAAL,CAAqBN,QAAQ,CAACN,IAA9B,EAAoC;AAChC,QAAA,MAAI,CAACO,YAAL,CAAkB;AAAEtB,UAAAA,MAAM,EAAEZ;AAAV,SAAlB,EAAsC;AAClC,cAAI,MAAI,CAACK,KAAL,CAAWmC,QAAf,EAAyB;AACrB,YAAA,MAAI,CAACnC,KAAL,CAAWmC,QAAX,CAAoB,MAAI,CAACJ,IAAzB;AACH;AACJ,SAJD;AAKH,OAND;AAOH,KAZD;AAaH;;SAEDb,qBAAA;AACI,QAAI,KAAKV,YAAL,KAAsB,IAAtB,IAA8B,KAAKA,YAAL,CAAkB8B,MAApD,EAA4D;AACxD,WAAK9B,YAAL,CAAkB8B,MAAlB;AACA,WAAK9B,YAAL,GAAoB,IAApB;AACH;AACJ;;SAEDqB,eAAA,sBAAaU,SAAb,EAA+BC,QAA/B;AACIA,IAAAA,QAAQ,GAAG,KAAKC,eAAL,CAAqBD,QAArB,CAAX;AACA,SAAKd,QAAL,CAAca,SAAd,EAAyBC,QAAzB;AACH;;SAEDC,kBAAA,yBAAgBD,QAAhB;;;AACI,QAAIE,MAAM,GAAG,IAAb;;AAEA,SAAKlC,YAAL,GAAoB;AAChB,UAAIkC,MAAJ,EAAY;AACRA,QAAAA,MAAM,GAAG,KAAT;AACA,QAAA,MAAI,CAAClC,YAAL,GAAoB,IAApB;AAEAgC,QAAAA,QAAQ;AACX;AACJ,KAPD;;AASC,SAAKhC,YAAL,CAAmC8B,MAAnC,GAA4C;AACzCI,MAAAA,MAAM,GAAG,KAAT;AACH,KAFA;;AAID,WAAO,KAAKlC,YAAZ;AACH;;SAED0B,kBAAA,yBAAgBd,OAAhB,EAAiCuB,OAAjC;AACI,SAAKF,eAAL,CAAqBE,OAArB;AACA,QAAMC,4BAA4B,GAAGxB,OAAO,IAAI,IAAX,IAAmB,CAAC,KAAKpB,KAAL,CAAW6C,cAApE;;AACA,QAAI,CAAC,KAAKd,IAAN,IAAca,4BAAlB,EAAgD;AAC5CE,MAAAA,UAAU,CAAC,KAAKtC,YAAN,EAAoC,CAApC,CAAV;AACA;AACH;;AAED,QAAI,KAAKR,KAAL,CAAW6C,cAAf,EAA+B;AAC3B,WAAK7C,KAAL,CAAW6C,cAAX,CAA0B,KAAKd,IAA/B,EAAqC,KAAKvB,YAA1C;AACH;;AAED,QAAIY,OAAO,IAAI,IAAf,EAAqB;AACjB0B,MAAAA,UAAU,CAAC,KAAKtC,YAAN,EAAoCY,OAApC,CAAV;AACH;AACJ;;SAED2B,SAAA;QACYxC,SAAW,KAAKD,MAAhBC;;AAER,QAAIA,MAAM,KAAKb,SAAf,EAA0B;AACtB,aAAO,IAAP;AACH;;sBAqBG,KAAKM;QAlBLgD,uBAAAA;QAEIC,AAeDC;;AAGP,WAAOF,QAAQ,CAACzC,MAAD,EAAS2C,UAAT,CAAf;AACH;;;;;;;AAvJG,UAAMnB,IAAI,0BAAG,KAAK/B,KAAL,CAAWmD,OAAd,wDAAG,oBAAoBC,OAAjC;;AACA,UAAI,CAACrB,IAAL,EAAW;AACP,cAAM,IAAIsB,KAAJ,CAAU,kDAAV,CAAN;AACH;;AACD,aAAOtB,IAAP;AACH;;;;EAlGoBvD,cAAK,CAAC8E;;AAuP/B,SAASC,IAAT;AAEC;;AAEAxD,UAAkB,CAACyD,YAAnB,GAAkC;AAC/B,QAAI,KAD2B;AAE/BnD,EAAAA,YAAY,EAAE,KAFiB;AAG/BD,EAAAA,aAAa,EAAE,KAHgB;AAI/BH,EAAAA,MAAM,EAAE,KAJuB;AAK/BoB,EAAAA,KAAK,EAAE,IALwB;AAM/BC,EAAAA,IAAI,EAAE,IANyB;AAQ/BU,EAAAA,OAAO,EAAEuB,IARsB;AAS/BtB,EAAAA,UAAU,EAAEsB,IATmB;AAU/BzB,EAAAA,SAAS,EAAEyB,IAVoB;AAY/BnB,EAAAA,MAAM,EAAEmB,IAZuB;AAa/BlB,EAAAA,SAAS,EAAEkB,IAboB;AAc/BpB,EAAAA,QAAQ,EAAEoB;AAdqB,CAAlC;;AC7SD;;;AAGA,AAEA;;;;;;;;AAOA,SAASE,MAAT,CACIC,GADJ,EAEIjE,KAFJ;AAII,MAAI,OAAOiE,GAAP,KAAe,UAAnB,EAA+B;AAC3BA,IAAAA,GAAG,CAACjE,KAAD,CAAH;AACH,GAFD,MAEO,IAAIiE,GAAJ,EAAS;AACZA,IAAAA,GAAG,CAACN,OAAJ,GAAc3D,KAAd;AACH;AACJ;;AAED,SAAwBkE,WACpBC,MACAC;AAEA;;;;;AAKA,SAAOrF,OAAA,CAAc;AACjB,QAAIoF,IAAI,IAAI,IAAR,IAAgBC,IAAI,IAAI,IAA5B,EAAkC;AAC9B,aAAO,IAAP;AACH;;AACD,WAAO,UAACC,QAAD;AACHL,MAAAA,MAAM,CAACG,IAAD,EAAOE,QAAP,CAAN;AACAL,MAAAA,MAAM,CAACI,IAAD,EAAOC,QAAP,CAAN;AACH,KAHD;AAIH,GARM,EAQJ,CAACF,IAAD,EAAOC,IAAP,CARI,CAAP;AASH;;SCvBuBE,mBAAmB/D;MAC/BoB,UAA8BpB,MAA9BoB;qBAA8BpB,MAArBgE;MAAAA,kCAAQ;MAAIC,OAASjE,MAATiE;AAC7B,SAAO;AACHC,IAAAA,QAAQ,EAAE,OAAO9C,OAAP,KAAmB,QAAnB,GAA8BA,OAAO,CAAC6C,IAAD,CAAP,IAAiB,CAA/C,GAAmD7C,OAD1D;AAEH+C,IAAAA,MAAM,EAAEH,KAAK,CAACI,wBAFX;AAGHC,IAAAA,KAAK,EAAEL,KAAK,CAACM;AAHV,GAAP;AAKH;;ACzBD;;;AAGA,AAAO,IAAMC,aAAa,GAAG;AACzB;AACAC,EAAAA,SAAS,EAAE,8BAFc;AAGzB;AACA;AACAC,EAAAA,OAAO,EAAE,8BALgB;AAMzB;AACAC,EAAAA,MAAM,EAAE,4BAPiB;AAQzB;AACAC,EAAAA,KAAK,EAAE;AATkB,CAAtB;AAYP;;;;AAGA,AAAO,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAC7C,IAAD;AAClB;AACA;AACA;AACAA,EAAAA,IAAI,CAAC8C,SAAL,GAAiB9C,IAAI,CAAC8C,SAAtB;AACH,CALM;;ACVP,IAAMC,QAAQ,GAAG,SAAXA,QAAW,CAACC,YAAD;AAAA,SAA6BC,IAAI,CAACC,KAAL,CAAWF,YAAX,CAA7B;AAAA,CAAjB;;AAEA,SAAwBG,iBACpBlF,OACAmF;MADAnF;AAAAA,IAAAA,QAA2B,CAAC,KAAD;;;aAG6CmF,OAAO,IAAI;2BAA3EjB;MAAAA,sCAAW;yBAAKC;MAAAA,kCAASI,aAAa,CAACC;wBAAWH;MAAAA,gCAAQ;;AAElE,MAAMe,UAAU,GAAGC,KAAK,CAACC,OAAN,CAActF,KAAd,IAAuBA,KAAvB,GAA+B,CAACA,KAAD,CAAlD;AAEA,SAAOoF,UAAU,CACZG,GADE,CACE,UAACC,YAAD;AACD,QAAMC,iBAAiB,GAAG,OAAOvB,QAAP,KAAoB,QAApB,GAA+BA,QAA/B,GAA0CY,QAAQ,CAACZ,QAAD,CAA5E;AACA,QAAMwB,cAAc,GAAG,OAAOrB,KAAP,KAAiB,QAAjB,GAA4BA,KAA5B,GAAoCS,QAAQ,CAACT,KAAD,CAAnE;AACA,WAAUmB,YAAV,SAA0BC,iBAA1B,SAA+CtB,MAA/C,SAAyDuB,cAAzD;AACH,GALE,EAMFC,IANE,CAMG,GANH,CAAP;AAOH;;ACdD,SAASC,aAAT,CAAuB7D,IAAvB;AACI,SAAQA,IAAI,IAAIA,IAAI,CAAC6D,aAAd,IAAgCC,QAAvC;AACH;;AAED,SAASC,WAAT,CAAqB/D,IAArB;AACI,MAAMgE,GAAG,GAAGH,aAAa,CAAC7D,IAAD,CAAzB;AACA,SAAOgE,GAAG,CAACC,WAAJ,IAAmBC,MAA1B;AACH;AAED;;;;;;AAIA,SAASC,QAAT,CAAkBC,IAAlB,EAAoCC,IAApC;MAAoCA;AAAAA,IAAAA,OAAO;;;AACvC,MAAIhF,OAAJ;;AACA,WAASiF,SAAT;;;sCAAsBC;AAAAA,MAAAA;;;AAClB,QAAMC,KAAK,GAAG,SAARA,KAAQ;AACV;AACAJ,MAAAA,IAAI,CAACK,KAAL,CAAW,KAAX,EAAiBF,IAAjB;AACH,KAHD;;AAIAG,IAAAA,YAAY,CAACrF,OAAD,CAAZ;AACAA,IAAAA,OAAO,GAAG0B,UAAU,CAACyD,KAAD,EAAQH,IAAR,CAApB;AACH;;AAEDC,EAAAA,SAAS,CAACK,KAAV,GAAkB;AACdD,IAAAA,YAAY,CAACrF,OAAD,CAAZ;AACH,GAFD;;AAIA,SAAOiF,SAAP;AACH;AAED;;;;;;AAIA,SAASM,iBAAT,CACIC,SADJ,EAEI7E,IAFJ;AAII,MAAM8E,IAAI,GAAG9E,IAAI,CAAC+E,qBAAL,EAAb;AACA,MAAMC,eAAe,GAAGjB,WAAW,CAAC/D,IAAD,CAAnC;AACA,MAAIiF,SAAJ;;AAEA,MAAIjF,IAAI,CAACkF,aAAT,EAAwB;AACpBD,IAAAA,SAAS,GAAGjF,IAAI,CAACkF,aAAjB;AACH,GAFD,MAEO;AACH,QAAMC,aAAa,GAAGH,eAAe,CAACI,gBAAhB,CAAiCpF,IAAjC,CAAtB;AACAiF,IAAAA,SAAS,GAAGE,aAAa,CAACE,gBAAd,CAA+B,mBAA/B,KAAuDF,aAAa,CAACE,gBAAd,CAA+B,WAA/B,CAAnE;AACH;;AAED,MAAIC,OAAO,GAAG,CAAd;AACA,MAAIC,OAAO,GAAG,CAAd;;AAEA,MAAIN,SAAS,IAAIA,SAAS,KAAK,MAA3B,IAAqC,OAAOA,SAAP,KAAqB,QAA9D,EAAwE;AACpE,QAAMO,eAAe,GAAGP,SAAS,CAACQ,KAAV,CAAgB,GAAhB,EAAqB,CAArB,EAAwBA,KAAxB,CAA8B,GAA9B,EAAmC,CAAnC,EAAsCA,KAAtC,CAA4C,GAA5C,CAAxB;AACAH,IAAAA,OAAO,GAAGI,QAAQ,CAACF,eAAe,CAAC,CAAD,CAAhB,EAAqB,EAArB,CAAlB;AACAD,IAAAA,OAAO,GAAGG,QAAQ,CAACF,eAAe,CAAC,CAAD,CAAhB,EAAqB,EAArB,CAAlB;AACH;;AAED,UAAQX,SAAR;AACI,SAAK,MAAL;AACI,8BAAqBG,eAAe,CAACW,UAAhB,GAA6BL,OAA7B,GAAuCR,IAAI,CAACc,IAAjE;;AACJ,SAAK,OAAL;AACI,+BAAsBd,IAAI,CAACc,IAAL,GAAYd,IAAI,CAACe,KAAjB,GAAyBP,OAA/C;;AACJ,SAAK,IAAL;AACI,8BAAqBN,eAAe,CAACc,WAAhB,GAA8BP,OAA9B,GAAwCT,IAAI,CAACiB,GAAlE;;AACJ;AACI;AACA,+BAAsBjB,IAAI,CAACiB,GAAL,GAAWjB,IAAI,CAACkB,MAAhB,GAAyBT,OAA/C;AATR;AAWH;;AAED,SAASU,iBAAT,CAA2BpB,SAA3B,EAAgE7E,IAAhE;AACI,MAAI,CAACA,IAAL,EAAW;AACX,MAAMiF,SAAS,GAAGL,iBAAiB,CAACC,SAAD,EAAY7E,IAAZ,CAAnC;;AACA,MAAIiF,SAAJ,EAAe;AACXjF,IAAAA,IAAI,CAACiC,KAAL,CAAWiE,eAAX,GAA6BjB,SAA7B;AACAjF,IAAAA,IAAI,CAACiC,KAAL,CAAWgD,SAAX,GAAuBA,SAAvB;AACH;AACJ;;AAED,IAAMkB,KAAK,gBAAG1J,UAAA,CAA2C,UAACwB,KAAD,EAAQ0D,GAAR;MAEjDV,WAUAhD,MAVAgD;yBAUAhD,MATA4G;MAAAA,0CAAY;MACRuB,SAQJnI;MAPAgE,QAOAhE,MAPAgE;uBAOAhE,MANAoB;MAAAA,sCAAU;MACVY,UAKAhC,MALAgC;MACAF,YAIA9B,MAJA8B;MACAM,SAGApC,MAHAoC;MACAD,WAEAnC,MAFAmC;MACGiG,sCACHpI;;AAEJ,MAAMmD,OAAO,GAAG3E,MAAA,CAAa,IAAb,CAAhB;AACA,MAAM6J,qBAAqB,GAAG1E,UAAU,CAAEX,QAAgB,CAACU,GAAnB,EAAwBP,OAAxB,CAAxC;AACA,MAAMmF,SAAS,GAAG3E,UAAU,CAAC0E,qBAAD,EAAwB3E,GAAxB,CAA5B;;AAEA,MAAM6E,WAAW,GAA+B,SAA1CA,WAA0C,CAACxG,IAAD,EAAOJ,WAAP;AAC5CqG,IAAAA,iBAAiB,CAACpB,SAAD,EAAY7E,IAAZ,CAAjB;AACA6C,IAAAA,MAAM,CAAC7C,IAAD,CAAN;;AAEA,QAAIC,OAAJ,EAAa;AACTA,MAAAA,OAAO,CAACD,IAAD,EAAOJ,WAAP,CAAP;AACH;AACJ,GAPD;;AASA,MAAM6G,cAAc,GAAG,SAAjBA,cAAiB,CAACzG,IAAD;AACnB,QAAMoC,MAAM,GAAG,CAAAH,KAAK,SAAL,IAAAA,KAAK,WAAL,YAAAA,KAAK,CAAEI,wBAAP,KAAmCG,aAAa,CAACE,OAAhE;AACA,QAAMgE,eAAe,GAAG1E,kBAAkB,CAAC;AACvC3C,MAAAA,OAAO,EAAPA,OADuC;AAEvC6C,MAAAA,IAAI,EAAE,OAFiC;AAGvCD,MAAAA,KAAK,eAAOA,KAAP;AAAcI,QAAAA,wBAAwB,EAAED;AAAxC;AAHkC,KAAD,CAA1C;AAMApC,IAAAA,IAAI,CAACiC,KAAL,CAAW0E,gBAAX,GAA8BxD,gBAAgB,CAAC,mBAAD,EAAsBuD,eAAtB,CAA9C;AACA1G,IAAAA,IAAI,CAACiC,KAAL,CAAW2E,UAAX,GAAwBzD,gBAAgB,CAAC,WAAD,EAAcuD,eAAd,CAAxC;AAEA1G,IAAAA,IAAI,CAACiC,KAAL,CAAWiE,eAAX,GAA6B,MAA7B;AACAlG,IAAAA,IAAI,CAACiC,KAAL,CAAWgD,SAAX,GAAuB,MAAvB;AACH,GAbD;;AAeA,MAAM4B,UAAU,GAA8B,SAAxCA,UAAwC,CAAC7G,IAAD;AAC1C,QAAMoC,MAAM,GAAG,CAAAH,KAAK,SAAL,IAAAA,KAAK,WAAL,YAAAA,KAAK,CAAEI,wBAAP,KAAmCG,aAAa,CAACI,KAAhE;AACA,QAAM8D,eAAe,GAAG1E,kBAAkB,CAAC;AACvC3C,MAAAA,OAAO,EAAPA,OADuC;AAEvC6C,MAAAA,IAAI,EAAE,MAFiC;AAGvCD,MAAAA,KAAK,eAAOA,KAAP;AAAcI,QAAAA,wBAAwB,EAAED;AAAxC;AAHkC,KAAD,CAA1C;AAMApC,IAAAA,IAAI,CAACiC,KAAL,CAAW0E,gBAAX,GAA8BxD,gBAAgB,CAAC,mBAAD,EAAsBuD,eAAtB,CAA9C;AACA1G,IAAAA,IAAI,CAACiC,KAAL,CAAW2E,UAAX,GAAwBzD,gBAAgB,CAAC,WAAD,EAAcuD,eAAd,CAAxC;AAEAT,IAAAA,iBAAiB,CAACpB,SAAD,EAAY7E,IAAZ,CAAjB;;AAEA,QAAIK,MAAJ,EAAY;AACRA,MAAAA,MAAM,CAACL,IAAD,CAAN;AACH;AACJ,GAhBD;;AAkBA,MAAM8G,YAAY,GAAgC,SAA5CA,YAA4C,CAAC9G,IAAD;AAC9C;AACAA,IAAAA,IAAI,CAACiC,KAAL,CAAW0E,gBAAX,GAA8B,EAA9B;AACA3G,IAAAA,IAAI,CAACiC,KAAL,CAAW2E,UAAX,GAAwB,EAAxB;;AAEA,QAAIxG,QAAJ,EAAc;AACVA,MAAAA,QAAQ,CAACJ,IAAD,CAAR;AACH;AACJ,GARD;;AAUA,MAAM+G,cAAc,GAAGtK,WAAA,CAAkB;AACrC,QAAI2E,OAAO,CAACC,OAAZ,EAAqB;AACjB4E,MAAAA,iBAAiB,CAACpB,SAAD,EAAYzD,OAAO,CAACC,OAApB,CAAjB;AACH;AACJ,GAJsB,EAIpB,CAACwD,SAAD,CAJoB,CAAvB;AAMApI,EAAAA,SAAA,CAAgB;AACZ;AACA,QAAI2J,MAAM,IAAIvB,SAAS,KAAK,MAAxB,IAAkCA,SAAS,KAAK,OAApD,EAA6D;AACzD,aAAOmC,SAAP;AACH;;AAED,QAAMC,YAAY,GAAG9C,QAAQ,CAAC;AAC1B,UAAI/C,OAAO,CAACC,OAAZ,EAAqB;AACjB4E,QAAAA,iBAAiB,CAACpB,SAAD,EAAYzD,OAAO,CAACC,OAApB,CAAjB;AACH;AACJ,KAJ4B,CAA7B;AAMA,QAAM2D,eAAe,GAAGjB,WAAW,CAAC3C,OAAO,CAACC,OAAT,CAAnC;AACA2D,IAAAA,eAAe,CAACkC,gBAAhB,CAAiC,QAAjC,EAA2CD,YAA3C;AACA,WAAO;AACHA,MAAAA,YAAY,CAACtC,KAAb;AACAK,MAAAA,eAAe,CAACmC,mBAAhB,CAAoC,QAApC,EAA8CF,YAA9C;AACH,KAHD;AAIH,GAlBD,EAkBG,CAACpC,SAAD,EAAYuB,MAAZ,CAlBH;AAoBA3J,EAAAA,SAAA,CAAgB;AACZ,QAAI,CAAC2J,MAAL,EAAa;AACT;AACA;AACAW,MAAAA,cAAc;AACjB;AACJ,GAND,EAMG,CAACX,MAAD,EAASW,cAAT,CANH;AAQA,SACItK,aAAA,CAAC2K,UAAD;AACIlJ,IAAAA,MAAM;AACNkD,IAAAA,OAAO,EAAEA;AACTnB,IAAAA,OAAO,EAAEuG;AACTzG,IAAAA,SAAS,EAAEA;AACXG,IAAAA,UAAU,EAAEuG;AACZpG,IAAAA,MAAM,EAAEwG;AACRzG,IAAAA,QAAQ,EAAE0G;AACV,UAAIV;AACJ/G,IAAAA,OAAO,EAAEA;KACLgH,MAVR,EAYK,UAAC9H,KAAD,EAAQ4C,UAAR;AAAA,WACG1E,YAAA,CAAmBwE,QAAnB;AACIU,MAAAA,GAAG,EAAE4E,SADT;AAEItE,MAAAA,KAAK;AACDoF,QAAAA,UAAU,EAAE9I,KAAK,KAAK,QAAV,IAAsB,CAAC6H,MAAvB,GAAgC,QAAhC,GAA2CY;AADtD,SAEE/E,KAFF,MAGGhB,QAAgB,CAAChD,KAAjB,CAAuBgE,KAH1B;AAFT,OAOOd,UAPP,EADH;AAAA,GAZL,CADJ;AA0BH,CAlIa,CAAd;AAoIAgF,KAAK,CAACmB,WAAN,GAAoB,OAApB;;AC9NA,IAAMC,OAAO,GAAG,SAAVA,OAAU,CAACtJ,KAAD;AAAA,SACZxB,4BAAA,MAAA;AACI+K,IAAAA,OAAO,EAAC;AACRC,IAAAA,SAAS,EAAC;AACVxF,IAAAA,KAAK,EAAE;AACHyF,MAAAA,QAAQ,EAAE,EADP;AAEHC,MAAAA,eAAe,EAAE,CAFd;AAGHC,MAAAA,UAAU,EAAE,MAHT;AAIH/B,MAAAA,KAAK,EAAE,KAJJ;AAKHG,MAAAA,MAAM,EAAE,KALL;AAMH6B,MAAAA,OAAO,EAAE,cANN;AAOHC,MAAAA,IAAI,EAAE,cAPH;AAQHC,MAAAA,UAAU,EAAE;AART;KAUH9J,MAbR,CADY;AAAA,CAAhB;;AAkBA,IAAM+J,SAAS,GAAa,SAAtBA,SAAsB;AAAA,SACxBvL,4BAAA,CAAC8K,OAAD,MAAA,EACI9K,4BAAA,OAAA;AACIwL,IAAAA,CAAC,EAAC;GADN,CADJ,CADwB;AAAA,CAA5B;;AASA,IAAMC,WAAW,GAAa,SAAxBA,WAAwB;AAAA,SAC1BzL,4BAAA,CAAC8K,OAAD,MAAA,EACI9K,4BAAA,OAAA;AAAMwL,IAAAA,CAAC,EAAC;GAAR,CADJ,CAD0B;AAAA,CAA9B;;AAMA,IAAME,SAAS,GAAa,SAAtBA,SAAsB;AAAA,SACxB1L,4BAAA,CAAC8K,OAAD,MAAA,EACI9K,4BAAA,OAAA;AACIwL,IAAAA,CAAC,EAAC;GADN,CADJ,CADwB;AAAA,CAA5B;;AAUA,IAAMG,QAAQ,GAAa,SAArBA,QAAqB;AAAA,SACvB3L,4BAAA,CAAC8K,OAAD,MAAA,EACI9K,4BAAA,OAAA;AACIwL,IAAAA,CAAC,EAAC;GADN,CADJ,CADuB;AAAA,CAA3B;;AASA,IAAMI,mBAAmB,GAAoC;AACzD,aAASrB,SADgD;AAEzDsB,EAAAA,OAAO,eAAE7L,4BAAA,CAACuL,SAAD,MAAA,CAFgD;AAGzDO,EAAAA,OAAO,eAAE9L,4BAAA,CAACyL,WAAD,MAAA,CAHgD;AAIzDM,EAAAA,KAAK,eAAE/L,4BAAA,CAAC0L,SAAD,MAAA,CAJkD;AAKzDM,EAAAA,IAAI,eAAEhM,4BAAA,CAAC2L,QAAD,MAAA;AALmD,CAA7D;;AClDO,IAAMM,QAAQ,GAAG;AACpBC,EAAAA,QAAQ,EAAE,CADU;AAEpBC,EAAAA,OAAO,EAAE,KAFW;AAGpBC,EAAAA,eAAe,EAAE,KAHG;AAIpBC,EAAAA,yBAAyB,EAAE,KAJP;AAKpBC,EAAAA,OAAO,EAAE,SALW;AAMpBC,EAAAA,gBAAgB,EAAE,IANE;AAOpBC,EAAAA,WAAW,EAAEZ,mBAPO;AAQpBa,EAAAA,YAAY,EAAE;AAAE3L,IAAAA,QAAQ,EAAE,QAAZ;AAAsBC,IAAAA,UAAU,EAAE;AAAlC,GARM;AASpB4J,EAAAA,mBAAmB,EAAEjB,KATD;AAUpBgD,EAAAA,kBAAkB,EAAE;AAChB7J,IAAAA,KAAK,EAAE,GADS;AAEhBC,IAAAA,IAAI,EAAE;AAFU;AAVA,CAAjB;AAgBP;;;;;AAIA,IAAM6J,mBAAmB,GAAG,SAAtBA,mBAAsB,CAACC,eAAD,EAAuBC,aAAvB;AACxB,MAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAACC,SAAD;AAAA,WAA8B,OAAOA,SAAP,KAAqB,QAArB,IAAiCA,SAAS,KAAK,IAA7E;AAAA,GAAvB;;AAEA,MAAID,cAAc,CAACF,eAAD,CAAlB,EAAqC,OAAOA,eAAP;AACrC,MAAIE,cAAc,CAACD,aAAD,CAAlB,EAAmC,OAAOA,aAAP;AACnC,SAAOZ,QAAQ,CAACM,gBAAhB;AACH,CAND;AAQA;;;;;;AAIA,IAAMS,qBAAqB,GAAG,SAAxBA,qBAAwB,CAACJ,eAAD,EAAuBC,aAAvB;AAC1B,MAAMI,EAAE,GAAG,SAALA,EAAK,CAACC,IAAD,EAAYC,KAAZ;AAAA,WAAgCA,KAAK,CAACC,IAAN,CAAW,UAACC,CAAD;AAAA,aAAO,OAAOH,IAAP,KAAgBG,CAAvB;AAAA,KAAX,CAAhC;AAAA,GAAX;;AAEA,MAAIJ,EAAE,CAACL,eAAD,EAAkB,CAAC,QAAD,EAAW,QAAX,CAAlB,CAAN,EAA+C;AAC3C,WAAOA,eAAP;AACH;;AAED,MAAIK,EAAE,CAACL,eAAD,EAAkB,CAAC,QAAD,CAAlB,CAAN,EAAqC;AACjC,wBACOX,QAAQ,CAACS,kBADhB,MAEQO,EAAE,CAACJ,aAAD,EAAgB,CAAC,QAAD,CAAhB,CAAF,IAAiCA,aAFzC,MAGOD,eAHP;AAKH;;AAED,MAAIK,EAAE,CAACJ,aAAD,EAAgB,CAAC,QAAD,EAAW,QAAX,CAAhB,CAAN,EAA6C;AACzC,WAAOA,aAAP;AACH;;AAED,MAAII,EAAE,CAACJ,aAAD,EAAgB,CAAC,QAAD,CAAhB,CAAN,EAAmC;AAC/B,wBACOZ,QAAQ,CAACS,kBADhB,MAEOG,aAFP;AAIH;;AAED,SAAOZ,QAAQ,CAACS,kBAAhB;AACH,CA3BD;;AA6BA,AAAO,IAAMY,KAAK,GACd,SADSA,KACT,CAAC3G,OAAD,EAAenF,KAAf;AAAA,SACA,UAAC+L,IAAD,EAA4BC,iBAA5B;QAA4BA;AAAAA,MAAAA,oBAAoB;;;AAC5C,QAAIA,iBAAJ,EAAuB;AACnB,0BACQvB,QAAgB,CAACsB,IAAD,CADxB,MAEO/L,KAAK,CAAC+L,IAAD,CAFZ,MAGO5G,OAAO,CAAC4G,IAAD,CAHd;AAKH;;AAED,QAAIA,IAAI,KAAK,kBAAb,EAAiC;AAC7B,aAAOZ,mBAAmB,CAAChG,OAAO,CAAC4F,gBAAT,EAA2B/K,KAAK,CAAC+K,gBAAjC,CAA1B;AACH;;AAED,QAAIgB,IAAI,KAAK,oBAAb,EAAmC;AAC/B,aAAOP,qBAAqB,CAACrG,OAAO,CAAC+F,kBAAT,EAA6BlL,KAAK,CAACkL,kBAAnC,CAA5B;AACH;;AAED,WAAO/F,OAAO,CAAC4G,IAAD,CAAP,IAAiB/L,KAAK,CAAC+L,IAAD,CAAtB,IAAiCtB,QAAgB,CAACsB,IAAD,CAAxD;AACH,GAnBD;AAAA,CADG;;SC/DSE,WACZC;AAEA,SAAOC,MAAM,CAACC,OAAP,CAAeF,MAAf,EAAuBG,MAAvB,CACH,UAACC,GAAD;AAAA;;AAAA,QAAOC,GAAP;AAAA,QAAY9M,KAAZ;AAAA,wBACO6M,GADP,6BAEKC,GAFL,IAEWC,GAAG,CAAC/M,KAAD,CAFd;AAAA,GADG,EAKH,EALG,CAAP;AAOH;AAED,AAAO,IAAMgN,gBAAgB,GAAG;AAC5BC,EAAAA,iBAAiB,EAAE,6BADS;AAE5BC,EAAAA,QAAQ,EAAE,oBAFkB;AAG5BC,EAAAA,eAAe,EAAE,2BAHW;AAI5BC,EAAAA,UAAU,EAAE,sBAJgB;AAK5BC,EAAAA,iBAAiB,EAAE,2BAAChC,OAAD;AAAA,qCAA6CA,OAA7C;AAAA;AALS,CAAzB;;ACDP,IAAMiC,OAAO,gBAAGd,UAAU,CAAC;AACvBe,EAAAA,IAAI,EAAE;AACFjF,IAAAA,MAAM,EAAE;AADN,GADiB;AAIvBkF,EAAAA,OAAO,EAAE;AACLlF,IAAAA,MAAM,EAAE;AADH;AAJc,CAAD,CAA1B;AASA,IAAMmF,aAAa,GAAG,KAAtB;AACA,IAAM9L,OAAO,GAAG,GAAhB;AAQA,IAAM+L,QAAQ,gBAAG3O,UAAA,CAAgD,UAACwB,KAAD,EAAQ0D,GAAR;MACrDV,WAAmChD,MAAnCgD;MAAcmF,SAAqBnI;MAAbmC,WAAanC,MAAbmC;AAE9B,MAAMiL,UAAU,GAAG5O,MAAA,CAA6B,IAA7B,CAAnB;AAEA,MAAM2E,OAAO,GAAG3E,MAAA,CAA6B,IAA7B,CAAhB;AACA,MAAM8J,SAAS,GAAG3E,UAAU,CAACD,GAAD,EAAMP,OAAN,CAA5B;;AAEA,MAAMkK,cAAc,GAAG,SAAjBA,cAAiB;AAAA,WAAOD,UAAU,CAAChK,OAAX,GAAqBgK,UAAU,CAAChK,OAAX,CAAmBkK,YAAxC,GAAuD,CAA9D;AAAA,GAAvB;;AAEA,MAAM/E,WAAW,GAA+B,SAA1CA,WAA0C,CAACxG,IAAD;AAC5CA,IAAAA,IAAI,CAACiC,KAAL,CAAW+D,MAAX,GAAoBmF,aAApB;AACH,GAFD;;AAIA,MAAM1E,cAAc,GAAG,SAAjBA,cAAiB,CAACzG,IAAD;AACnB,QAAMwL,WAAW,GAAGF,cAAc,EAAlC;;8BAEiDtJ,kBAAkB,CAAC;AAChE3C,MAAAA,OAAO,EAAPA,OADgE;AAEhE6C,MAAAA,IAAI,EAAE;AAF0D,KAAD;QAAjDiH,yCAAVhH;QAA8BC,6BAAAA;;AAKtCpC,IAAAA,IAAI,CAACiC,KAAL,CAAWkH,kBAAX,GACI,OAAOA,kBAAP,KAA8B,QAA9B,GAAyCA,kBAAzC,GAAiEA,kBAAjE,OADJ;AAGAnJ,IAAAA,IAAI,CAACiC,KAAL,CAAW+D,MAAX,GAAuBwF,WAAvB;AACAxL,IAAAA,IAAI,CAACiC,KAAL,CAAWI,wBAAX,GAAsCD,MAAM,IAAI,EAAhD;AACH,GAbD;;AAeA,MAAMqJ,aAAa,GAAiC,SAA9CA,aAA8C,CAACzL,IAAD;AAChDA,IAAAA,IAAI,CAACiC,KAAL,CAAW+D,MAAX,GAAoB,MAApB;AACH,GAFD;;AAIA,MAAMa,UAAU,GAA8B,SAAxCA,UAAwC,CAAC7G,IAAD;AAC1CA,IAAAA,IAAI,CAACiC,KAAL,CAAW+D,MAAX,GAAuBsF,cAAc,EAArC;AACH,GAFD;;AAIA,MAAMI,aAAa,GAAG,SAAhBA,aAAgB,CAAC1L,IAAD;AAClB6C,IAAAA,MAAM,CAAC7C,IAAD,CAAN;;+BAEiDgC,kBAAkB,CAAC;AAChE3C,MAAAA,OAAO,EAAPA,OADgE;AAEhE6C,MAAAA,IAAI,EAAE;AAF0D,KAAD;QAAjDiH,0CAAVhH;QAA8BC,8BAAAA;;AAKtCpC,IAAAA,IAAI,CAACiC,KAAL,CAAWkH,kBAAX,GACI,OAAOA,kBAAP,KAA8B,QAA9B,GAAyCA,kBAAzC,GAAiEA,kBAAjE,OADJ;AAEAnJ,IAAAA,IAAI,CAACiC,KAAL,CAAW+D,MAAX,GAAoBmF,aAApB;AACAnL,IAAAA,IAAI,CAACiC,KAAL,CAAWI,wBAAX,GAAsCD,MAAM,IAAI,EAAhD;AACH,GAZD;;AAcA,SACI3F,aAAA,CAAC2K,UAAD;AACI,UAAIhB;AACJ/H,IAAAA,aAAa;AACb4B,IAAAA,OAAO,EAAEuG;AACTzG,IAAAA,SAAS,EAAE0L;AACXvL,IAAAA,UAAU,EAAEuG;AACZpG,IAAAA,MAAM,EAAEwG;AACRzG,IAAAA,QAAQ,EAAEA;AACVE,IAAAA,SAAS,EAAEoL;AACXtK,IAAAA,OAAO,EAAEA;AACT/B,IAAAA,OAAO,EAAEA;GAVb,EAYK,UAACd,KAAD,EAAQ4C,UAAR;AAAA,WACG1E,aAAA,MAAA;AACIkF,MAAAA,GAAG,EAAE4E;AACLoF,MAAAA,SAAS,EAAEC,IAAI,CAACZ,OAAO,CAACC,IAAT,EAAoC1M,KAAK,KAAK,SAA9C,IAAkByM,OAAO,CAACE,OAA1B;AACfjJ,MAAAA,KAAK;AACD4J,QAAAA,aAAa,EAAE,KADd;AAEDC,QAAAA,QAAQ,EAAE,QAFT;AAGDC,QAAAA,SAAS,EAAEZ,aAHV;AAIDvE,QAAAA,UAAU,EAAEzD,gBAAgB,CAAC,QAAD;AAJ3B,SAKG5E,KAAK,KAAK,SAAV,IAAuB;AACvBuN,QAAAA,QAAQ,EAAE;AADa,OAL1B,MAQGvN,KAAK,KAAK,QAAV,IACA,CAAC6H,MADD,IACW;AACPiB,QAAAA,UAAU,EAAE;AADL,OATd;OAaDlG,WAhBR,EAkBI1E,aAAA,MAAA;AACIkF,MAAAA,GAAG,EAAE0J;AACLM,MAAAA,SAAS,EAAEjB,gBAAgB,CAACG;AAC5B;AACA5I,MAAAA,KAAK,EAAE;AAAE4F,QAAAA,OAAO,EAAE,MAAX;AAAmBhC,QAAAA,KAAK,EAAE;AAA1B;KAJX,EAMK5E,QANL,CAlBJ,CADH;AAAA,GAZL,CADJ;AA4CH,CA/FgB,CAAjB;AAiGAmK,QAAQ,CAAC9D,WAAT,GAAuB,UAAvB;;ACrHA,IAAMzC,SAAS,GAA6C;AACxDmH,EAAAA,KAAK,EAAE,MADiD;AAExDpG,EAAAA,IAAI,EAAE,OAFkD;AAGxDqG,EAAAA,MAAM,EAAE,IAHgD;AAIxDlG,EAAAA,GAAG,EAAE;AAJmD,CAA5D;AAOA,AAAO,IAAMmG,iBAAiB,GAAG,SAApBA,iBAAoB,CAAChD,YAAD;AAC7B,MAAIA,YAAY,CAAC1L,UAAb,KAA4B,QAAhC,EAA0C;AACtC,WAAOqH,SAAS,CAACqE,YAAY,CAAC1L,UAAd,CAAhB;AACH;;AACD,SAAOqH,SAAS,CAACqE,YAAY,CAAC3L,QAAd,CAAhB;AACH,CALM;AAOP;;AACA,AAAO,IAAM4O,sBAAsB,GAAG,SAAzBA,sBAAyB,CAACjD,YAAD;AAAA,0BACnB7L,kBAAkB,CAAC6L,YAAD,CADC;AAAA,CAA/B;AAGP;;;;AAGA,AAAO,IAAMkD,qBAAqB,GAAG,SAAxBA,qBAAwB,CACjCpB,OADiC;MACjCA;AAAAA,IAAAA,UAA4C;;;AAE5C,MAAMqB,gBAAgB,GAAoC;AACtDC,IAAAA,aAAa,EAAE,IADuC;AAEtDC,IAAAA,8BAA8B,EAAE,IAFsB;AAGtDC,IAAAA,iCAAiC,EAAE,IAHmB;AAItDC,IAAAA,6BAA6B,EAAE,IAJuB;AAKtDC,IAAAA,gCAAgC,EAAE,IALoB;AAMtDC,IAAAA,4BAA4B,EAAE,IANwB;AAOtDC,IAAAA,+BAA+B,EAAE;AAPqB,GAA1D;AASA,SAAQxC,MAAM,CAACyC,IAAP,CAAY7B,OAAZ,EACH8B,MADG,CACI,UAACtC,GAAD;AAAA,WAAS,CAAC6B,gBAAgB,CAAC7B,GAAD,CAA1B;AAAA,GADJ,EAEHF,MAFG,CAEI,UAACyC,GAAD,EAAMvC,GAAN;AAAA;;AAAA,wBAAoBuC,GAApB,6BAA0BvC,GAA1B,IAAgCQ,OAAO,CAACR,GAAD,CAAvC;AAAA,GAFJ,EAEqD,EAFrD,CAAR;AAGH,CAfM;;AC9BP,IAAMhO,MAAI,GAAG,SAAPA,IAAO;AACT;AACH,CAFD;AAIA;;;;;;;;;AAOA,SAAwBwQ,sBACpBC,OACAC;AAEA;AACA,SAAOD,KAAK,CAAC3C,MAAN,CAAa,UAACC,GAAD,EAAMnG,IAAN;AAChB,QAAIA,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK4C,SAA9B,EAAyC;AACrC,aAAOuD,GAAP;AACH;;AAED,WAAO,SAAS4C,eAAT;wCAA4B5I;AAAAA,QAAAA;;;AAC/B,UAAM6I,MAAM,aAAO7I,IAAP,CAAZ;;AACA,UAAI2I,UAAU,IAAIE,MAAM,CAACC,OAAP,CAAeH,UAAf,MAA+B,CAAC,CAAlD,EAAqD;AACjDE,QAAAA,MAAM,CAACE,IAAP,CAAYJ,UAAZ;AACH;;;AAED3C,MAAAA,GAAG,CAAC9F,KAAJ,CAAU,IAAV,EAAgB2I,MAAhB;AACAhJ,MAAAA,IAAI,CAACK,KAAL,CAAW,IAAX,EAAiB2I,MAAjB;AACH,KARD;AASH,GAdM,EAcJ5Q,MAdI,CAAP;AAeH;;ACjCD;;;;AAIA,AAEA,IAAM+Q,iBAAiB,GAAG,OAAOrJ,MAAP,KAAkB,WAAlB,GAAgCzH,eAAhC,GAAwDA,SAAlF;AAEA,SAAwB+Q,iBACpBC;AAEA,MAAM9L,GAAG,GAAGlF,MAAA,CAAagR,EAAb,CAAZ;AACAF,EAAAA,iBAAiB,CAAC;AACd5L,IAAAA,GAAG,CAACN,OAAJ,GAAcoM,EAAd;AACH,GAFgB,CAAjB;AAGA,SAAOhR,WAAA,CACH;AAAA;AAEI,OAAC,AAAGkF,GAAG,CAACN,OAAR;AAFJ;AAAA,GADG,EAIH,EAJG,CAAP;AAMH;;ACrBD;;;AAGA,AAeA,IAAMuJ,QAAQ,gBAAGnO,UAAA,CAAgD,UAACwB,KAAD,EAAQ0D,GAAR;MAEzDV,WAQAhD,MARAgD;MACA0K,YAOA1N,MAPA0N;MACA3C,mBAMA/K,MANA+K;8BAMA/K,MALA6K;MAAAA,+DAA4B;MAC5B4E,UAIAzP,MAJAyP;MACAC,KAGA1P,MAHA0P;MACAC,OAEA3P,MAFA2P;6BAEA3P,MADA4P;MAAAA,kDAAgB;AAGpB,MAAMC,aAAa,GAAGrR,MAAA,EAAtB;AAEA,MAAMsR,WAAW,GAAGP,gBAAgB,CAAC;AACjC,QAAIE,OAAJ,EAAa;AACTA,MAAAA,OAAO,MAAP;AACH;AACJ,GAJmC,CAApC;AAMA,MAAMM,gBAAgB,GAAGR,gBAAgB,CAAC,UAACS,qBAAD;AACtC,QAAI,CAACP,OAAD,IAAYO,qBAAqB,IAAI,IAAzC,EAA+C;AAC3C;AACH;;AAED,QAAIH,aAAa,CAACzM,OAAlB,EAA2B;AACvBqD,MAAAA,YAAY,CAACoJ,aAAa,CAACzM,OAAf,CAAZ;AACH;;AACDyM,IAAAA,aAAa,CAACzM,OAAd,GAAwBN,UAAU,CAAC;AAC/BgN,MAAAA,WAAW,CAAC,IAAD,EAAO,SAAP,EAAkBJ,EAAlB,CAAX;AACH,KAFiC,EAE/BM,qBAF+B,CAAlC;AAGH,GAXwC,CAAzC;AAaAxR,EAAAA,SAAA,CAAgB;AACZ,QAAImR,IAAJ,EAAU;AACNI,MAAAA,gBAAgB,CAAChF,gBAAD,CAAhB;AACH;;AAED,WAAO;AACH,UAAI8E,aAAa,CAACzM,OAAlB,EAA2B;AACvBqD,QAAAA,YAAY,CAACoJ,aAAa,CAACzM,OAAf,CAAZ;AACH;AACJ,KAJD;AAKH,GAVD,EAUG,CAACuM,IAAD,EAAO5E,gBAAP,EAAyBgF,gBAAzB,CAVH;AAYA;;;;;AAIA,MAAME,WAAW,GAAG,SAAdA,WAAc;AAChB,QAAIJ,aAAa,CAACzM,OAAlB,EAA2B;AACvBqD,MAAAA,YAAY,CAACoJ,aAAa,CAACzM,OAAf,CAAZ;AACH;AACJ,GAJD;AAMA;;;;;;AAIA,MAAM8M,YAAY,GAAG1R,WAAA,CAAkB;AACnC,QAAIuM,gBAAgB,IAAI,IAAxB,EAA8B;AAC1BgF,MAAAA,gBAAgB,CAAChF,gBAAgB,GAAG,GAApB,CAAhB;AACH;AACJ,GAJoB,EAIlB,CAACA,gBAAD,EAAmBgF,gBAAnB,CAJkB,CAArB;;AAMA,MAAMI,gBAAgB,GAA4C,SAA5DA,gBAA4D,CAACC,KAAD;AAC9D,QAAIR,aAAa,CAACS,YAAlB,EAAgC;AAC5BT,MAAAA,aAAa,CAACS,YAAd,CAA2BD,KAA3B;AACH;;AACDH,IAAAA,WAAW;AACd,GALD;;AAOA,MAAMK,gBAAgB,GAA4C,SAA5DA,gBAA4D,CAACF,KAAD;AAC9D,QAAIR,aAAa,CAACW,YAAlB,EAAgC;AAC5BX,MAAAA,aAAa,CAACW,YAAd,CAA2BH,KAA3B;AACH;;AACDF,IAAAA,YAAY;AACf,GALD;;AAOA1R,EAAAA,SAAA,CAAgB;AACZ,QAAI,CAACqM,yBAAD,IAA8B8E,IAAlC,EAAwC;AACpC1J,MAAAA,MAAM,CAACgD,gBAAP,CAAwB,OAAxB,EAAiCiH,YAAjC;AACAjK,MAAAA,MAAM,CAACgD,gBAAP,CAAwB,MAAxB,EAAgCgH,WAAhC;AAEA,aAAO;AACHhK,QAAAA,MAAM,CAACiD,mBAAP,CAA2B,OAA3B,EAAoCgH,YAApC;AACAjK,QAAAA,MAAM,CAACiD,mBAAP,CAA2B,MAA3B,EAAmC+G,WAAnC;AACH,OAHD;AAIH;;AAED,WAAOlH,SAAP;AACH,GAZD,EAYG,CAAC8B,yBAAD,EAA4BqF,YAA5B,EAA0CP,IAA1C,CAZH;AAcA,SACInR,aAAA,MAAA;AACIkF,IAAAA,GAAG,EAAEA;KACDkM;AACJlC,IAAAA,SAAS,EAAEC,IAAI,CAAClB,gBAAgB,CAACE,QAAlB,EAA4Be,SAA5B;AACf2C,IAAAA,YAAY,EAAEF;AACdI,IAAAA,YAAY,EAAED;IALlB,EAOKtN,QAPL,CADJ;AAWH,CAxGgB,CAAjB;AA0GA2J,QAAQ,CAACtD,WAAT,GAAuB,UAAvB;;;ACtHA,IAAM0D,SAAO,gBAAGd,UAAU,CAAC;AACvBe,EAAAA,IAAI;AACApD,IAAAA,OAAO,EAAE,MADT;AAEA4G,IAAAA,QAAQ,EAAE,MAFV;AAGAC,IAAAA,QAAQ,EAAE;AAHV,WAIC7R,WAAW,CAACE,IAJb,IAIoB;AAChB2R,IAAAA,QAAQ,EAAE,SADM;AAEhBC,IAAAA,QAAQ,EAAE;AAFM,GAJpB;AADmB,CAAD,CAA1B;AAYA,IAAMC,eAAe,gBAAGC,UAAU,CAAuC,gBAA0BlN,GAA1B;AAAA,MAAGgK,SAAH,QAAGA,SAAH;AAAA,MAAiB1N,KAAjB;;AAAA,SACrExB,4BAAA,MAAA;AAAKkF,IAAAA,GAAG,EAAEA;AAAKgK,IAAAA,SAAS,EAAEC,IAAI,CAACZ,SAAO,CAACC,IAAT,EAAeU,SAAf;KAA+B1N,MAA7D,CADqE;AAAA,CAAvC,CAAlC;AAIA2Q,eAAe,CAACtH,WAAhB,GAA8B,iBAA9B;;AChBA,IAAM0D,SAAO,gBAAGd,UAAU,CAAC;AACvBe,EAAAA,IAAI,EAAE;AACF6D,IAAAA,eAAe,EAAE,SADf;AAEFpH,IAAAA,QAAQ,EAAE,UAFR;AAGFqH,IAAAA,UAAU,EAAE,IAHV;AAIFC,IAAAA,aAAa,EAAE,WAJb;AAKFC,IAAAA,KAAK,EAAE,MALL;AAMFC,IAAAA,UAAU,EAAE,QANV;AAOFC,IAAAA,OAAO,EAAE,UAPP;AAQFC,IAAAA,YAAY,EAAE,KARZ;AASFC,IAAAA,SAAS,EACL;AAVF,GADiB;AAavBC,EAAAA,WAAW,EAAE;AACTC,IAAAA,WAAW,EAAK,IAAI,GAAT;AADF,GAbU;AAgBvB,aAAS;AACLT,IAAAA,eAAe,EAAE;AADZ,GAhBc;AAmBvBxG,EAAAA,OAAO,EAAE;AACLwG,IAAAA,eAAe,EAAE;AADZ,GAnBc;AAsBvBtG,EAAAA,KAAK,EAAE;AACHsG,IAAAA,eAAe,EAAE;AADd,GAtBgB;AAyBvBvG,EAAAA,OAAO,EAAE;AACLuG,IAAAA,eAAe,EAAE;AADZ,GAzBc;AA4BvBrG,EAAAA,IAAI,EAAE;AACFqG,IAAAA,eAAe,EAAE;AADf,GA5BiB;AA+BvBU,EAAAA,OAAO,EAAE;AACL3H,IAAAA,OAAO,EAAE,MADJ;AAELqH,IAAAA,UAAU,EAAE,QAFP;AAGLC,IAAAA,OAAO,EAAE;AAHJ,GA/Bc;AAoCvBM,EAAAA,MAAM,EAAE;AACJ5H,IAAAA,OAAO,EAAE,MADL;AAEJqH,IAAAA,UAAU,EAAE,QAFR;AAGJQ,IAAAA,UAAU,EAAE,MAHR;AAIJH,IAAAA,WAAW,EAAE,MAJT;AAKJI,IAAAA,WAAW,EAAE;AALT;AApCe,CAAD,CAA1B;AA6CA,IAAMC,eAAe,GAAG,oBAAxB;AAEA,IAAMC,qBAAqB,gBAAGhB,UAAU,CAAqC,UAAC5Q,KAAD,EAAQ6R,YAAR;MAErEnC,KAQA1P,MARA0P;MACA6B,UAOAvR,MAPAuR;MACQO,4BAMR9R,MANAwR;MACAxG,cAKAhL,MALAgL;MACAF,UAIA9K,MAJA8K;MACAF,kBAGA5K,MAHA4K;MACA5G,QAEAhE,MAFAgE;MACA0J,YACA1N,MADA0N;AAGJ,MAAMqE,IAAI,GAAG/G,WAAW,CAACF,OAAD,CAAxB;AAEA,MAAI0G,MAAM,GAAGM,yBAAb;;AACA,MAAI,OAAON,MAAP,KAAkB,UAAtB,EAAkC;AAC9BA,IAAAA,MAAM,GAAGA,MAAM,CAAC9B,EAAD,CAAf;AACH;;AAED,SACIlR,4BAAA,CAACmS,eAAD;AACIjN,IAAAA,GAAG,EAAEmO;AACLG,IAAAA,IAAI,EAAC;wBACaL;AAClB3N,IAAAA,KAAK,EAAEA;AACP0J,IAAAA,SAAS,EAAEC,IAAI,CACXlB,gBAAgB,CAACI,UADN,EAEXJ,gBAAgB,CAACK,iBAAjB,CAAmChC,OAAnC,CAFW,EAGXiC,SAAO,CAACC,IAHG,EAKXD,SAAO,CAACjC,OAAD,CALI,EAMX4C,SANW,EAIc,CAAC9C,eAJf,IAIkCmH,IAJlC,IAIRhF,SAAO,CAACsE,WAJA;GALnB,EAcI7S,4BAAA,MAAA;AAAKkR,IAAAA,EAAE,EAAEiC;AAAiBjE,IAAAA,SAAS,EAAEX,SAAO,CAACwE;GAA7C,EACK,CAAC3G,eAAD,GAAmBmH,IAAnB,GAA0B,IAD/B,EAEKR,OAFL,CAdJ,EAkBKC,MAAM,IAAIhT,4BAAA,MAAA;AAAKkP,IAAAA,SAAS,EAAEX,SAAO,CAACyE;GAAxB,EAAiCA,MAAjC,CAlBf,CADJ;AAsBH,CAzCuC,CAAxC;AA2CAI,qBAAqB,CAACvI,WAAtB,GAAoC,uBAApC;AAEA,2CAAe4I,IAAI,CAACL,qBAAD,CAAnB;;AClFA,IAAM1F,MAAM,gBAAGD,UAAU,CAAC;AACtBiG,EAAAA,WAAW,EAAE;AACTtK,IAAAA,KAAK,EAAE,MADE;AAETuK,IAAAA,QAAQ,EAAE,UAFD;AAGTnL,IAAAA,SAAS,EAAE,eAHF;AAITc,IAAAA,GAAG,EAAE,CAJI;AAKTiG,IAAAA,KAAK,EAAE,CALE;AAMTC,IAAAA,MAAM,EAAE,CANC;AAOTrG,IAAAA,IAAI,EAAE,CAPG;AAQT+I,IAAAA,QAAQ,EAAE;AARD;AADS,CAAD,CAAzB;;AAqBA,IAAM0B,YAAY,GAAgC,SAA5CA,YAA4C,CAACpS,KAAD;AAC9C,MAAMoB,OAAO,GAAGiR,MAAM,EAAtB;;kBACkCC,QAAQ,CAAC,IAAD;MAAnCC;MAAWC;;AAElB,MAAM1C,WAAW,GAAwCf,qBAAqB,CAAC,CAC3E/O,KAAK,CAACyS,KAAN,CAAYhD,OAD+D,EAE3EzP,KAAK,CAACyP,OAFqE,CAAD,CAA9E;;AAKA,MAAMjC,aAAa,GAAwC,SAArDA,aAAqD;AACvD,QAAIxN,KAAK,CAACyS,KAAN,CAAYC,YAAhB,EAA8B;AAC1B5C,MAAAA,WAAW,CAAC,IAAD,EAAO,YAAP,EAAqB9P,KAAK,CAACyS,KAAN,CAAY/C,EAAjC,CAAX;AACH;AACJ,GAJD;;AAMA,MAAMiD,kBAAkB,GAAGC,WAAW,CAAC;AACnCxR,IAAAA,OAAO,CAACgC,OAAR,GAAkBN,UAAU,CAAC;AACzB0P,MAAAA,YAAY,CAAC,UAACK,GAAD;AAAA,eAAS,CAACA,GAAV;AAAA,OAAD,CAAZ;AACH,KAF2B,EAEzB,GAFyB,CAA5B;AAGH,GAJqC,EAInC,EAJmC,CAAtC;AAMAC,EAAAA,SAAS,CACL;AAAA,WAAM;AACF,UAAI1R,OAAO,CAACgC,OAAZ,EAAqB;AACjBqD,QAAAA,YAAY,CAACrF,OAAO,CAACgC,OAAT,CAAZ;AACH;AACJ,KAJD;AAAA,GADK,EAML,EANK,CAAT;MASQqP,QAAkEzS,MAAlEyS;MAAgBM,aAAkD/S,MAA3D+M;yBAA2D/M,MAAtCsD;MAAAA,0CAAYsO;AAEhD,MAAM7E,OAAO,GAAGiG,OAAO,CAAC;AAAA,WAAM7E,qBAAqB,CAAC4E,UAAD,CAA3B;AAAA,GAAD,EAA0C,CAACA,UAAD,CAA1C,CAAvB;;MAGIpD,OAcA8C,MAdA9C;MACAC,gBAaA6C,MAbA7C;MACAzG,sBAYAsJ,MAZAtJ;MACA8J,kBAWAR,MAXAQ;MACA/H,qBAUAuH,MAVAvH;MACAL,4BASA4H,MATA5H;MACSqI,6BAQTT,MARAU;MACSC,AAMNC,2CACHZ;;AAEJ,MAAMhK,eAAe;AACjB7B,IAAAA,SAAS,EAAEqH,iBAAiB,CAACoF,UAAU,CAACpI,YAAZ,CADX;AAEjB7J,IAAAA,OAAO,EAAE8J;AAFQ,KAGd+H,eAHc,CAArB;;AAMA,MAAIE,OAAO,GAAGD,0BAAd;;AACA,MAAI,OAAOC,OAAP,KAAmB,UAAvB,EAAmC;AAC/BA,IAAAA,OAAO,GAAGA,OAAO,CAACE,UAAU,CAAC3D,EAAZ,EAAgB2D,UAAU,CAAC9B,OAA3B,CAAjB;AACH;;AAED,MAAM+B,SAAS,GACX,CAAC,SAAD,EAAY,WAAZ,EAAyB,QAAzB,EAAmC,UAAnC,EACFjH,MADE,CAEA,UAACC,GAAD,EAAMiH,MAAN;AAAA;;AAAA,wBACOjH,GADP,6BAEKiH,MAFL,IAEcxE,qBAAqB,CAAC,CAAC/O,KAAK,CAACyS,KAAN,CAAYc,MAAZ,CAAD,EAA6BvT,KAAK,CAACuT,MAAD,CAAlC,CAAD,EAAqDF,UAAU,CAAC3D,EAAhE,CAFnC;AAAA,GAFA,EAMA,EANA,CADJ;AAUA,SACIlR,4BAAA,CAAC2O,QAAD;AAAU,UAAIoF;AAAWpQ,IAAAA,QAAQ,EAAEmR,SAAS,CAACnR;GAA7C,EACI3D,4BAAA,CAACmO,QAAD;AACIgD,IAAAA,IAAI,EAAEA;AACND,IAAAA,EAAE,EAAE2D,UAAU,CAAC3D;AACf7E,IAAAA,yBAAyB,EAAEA;AAC3BE,IAAAA,gBAAgB,EAAEsI,UAAU,CAACtI;AAC7B2C,IAAAA,SAAS,EAAEC,IAAI,CACXzB,MAAM,CAACgG,WADI,EAEXnF,OAAO,CAACC,IAFG,EAGXD,OAAO,CAACmB,sBAAsB,CAACmF,UAAU,CAACpI,YAAZ,CAAvB,CAHI;AAKf2E,IAAAA,aAAa,EAAEA;AACfH,IAAAA,OAAO,EAAEK;GAXb,EAaItR,4BAAA,CAAC2K,mBAAD,oBACQV;AACJxI,IAAAA,MAAM;AACN,UAAI0P;AACJvN,IAAAA,MAAM,EAAEkR,SAAS,CAAClR;AAClBD,IAAAA,QAAQ,EAAEwQ;AACV3Q,IAAAA,OAAO,EAAEsR,SAAS,CAACtR;AACnB;AACA;AACAF,IAAAA,SAAS,EAAEiN,qBAAqB,CAAC,CAACuE,SAAS,CAACxR,SAAX,EAAsB0L,aAAtB,CAAD,EAAuC6F,UAAU,CAAC3D,EAAlD;IATpC,EAWMyD,OAA8B,IAAI3U,4BAAA,CAAC8E,SAAD,oBAAe+P,WAAf,CAXxC,CAbJ,CADJ,CADJ;AA+BH,CAvGD;;;AC9BA,IAAMG,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE;AAAE,eAAS,EAAX;AAAeC,IAAAA,KAAK,EAAE;AAAtB,GADM;AAEZC,EAAAA,QAAQ,EAAE;AAAE,eAAS,CAAX;AAAcD,IAAAA,KAAK,EAAE;AAArB;AAFE,CAAhB;AAKA,IAAME,eAAe,SAAOnH,gBAAgB,CAACG,eAA7C;AAEA,IAAMiH,aAAa,GAAG,EAAtB;AAEA,IAAM3H,QAAM,gBAAGD,UAAU,CAAC;AACtBe,EAAAA,IAAI;AACA8G,IAAAA,SAAS,EAAE,YADX;AAEAlK,IAAAA,OAAO,EAAE,MAFT;AAGAmK,IAAAA,SAAS,EAAE,MAHX;AAIA5B,IAAAA,QAAQ,EAAE,OAJV;AAKA6B,IAAAA,MAAM,EAAE,IALR;AAMAjM,IAAAA,MAAM,EAAE,MANR;AAOAH,IAAAA,KAAK,EAAE,MAPP;AAQAe,IAAAA,UAAU,eAAEzD,gBAAgB,CAAC,CAAC,KAAD,EAAQ,OAAR,EAAiB,QAAjB,EAA2B,MAA3B,EAAmC,WAAnC,CAAD,EAAkD;AAC1EhB,MAAAA,QAAQ,EAAE,GADgE;AAE1EC,MAAAA,MAAM,EAAE;AAFkE,KAAlD,CAR5B;AAYA;AACA;AACAyJ,IAAAA,aAAa,EAAE;AAdf,aAeCgG,eAfD,IAemB;AACf1C,IAAAA,OAAO,EAAKsC,OAAO,CAACG,QAAR,WAAL,WADQ;AAEfhL,IAAAA,UAAU,EAAE;AAFG,GAfnB,UAmBAsL,QAnBA,oBAmByBT,OAAO,CAACC,IAAR,cAAuB,CAnBhD,kBAoBC7U,WAAW,CAACC,MApBb,IAoBsB;AAClB+I,IAAAA,KAAK,EAAE,MADW;AAElBqM,IAAAA,QAAQ,mBAAiBJ,aAAa,GAAG,CAAjC;AAFU,GApBtB,UADkB;AA0BtBK,EAAAA,SAAS,+BACJN,eADI,IACc;AACf1C,IAAAA,OAAO,EAAKsC,OAAO,CAACG,QAAR,CAAiBD,KAAtB;AADQ,GADd,aA1Ba;AA+BtB5L,EAAAA,GAAG,EAAE;AACDA,IAAAA,GAAG,EAAK0L,OAAO,CAACC,IAAR,cAAuBD,OAAO,CAACG,QAAR,WAA5B,OADF;AAEDQ,IAAAA,aAAa,EAAE;AAFd,GA/BiB;AAmCtBnG,EAAAA,MAAM,EAAE;AACJA,IAAAA,MAAM,EAAKwF,OAAO,CAACC,IAAR,cAAuBD,OAAO,CAACG,QAAR,WAA5B,OADF;AAEJQ,IAAAA,aAAa,EAAE;AAFX,GAnCc;AAuCtBxM,EAAAA,IAAI;AACAA,IAAAA,IAAI,EAAK6L,OAAO,CAACC,IAAR,WAAL;AADJ,WAEC7U,WAAW,CAACE,IAFb,IAEoB;AAChBmS,IAAAA,UAAU,EAAE;AADI,GAFpB,QAKCrS,WAAW,CAACC,MALb,IAKsB;AAClB8I,IAAAA,IAAI,EAAKkM,aAAL;AADc,GALtB,QAvCkB;AAgDtB9F,EAAAA,KAAK;AACDA,IAAAA,KAAK,EAAKyF,OAAO,CAACC,IAAR,WAAL;AADJ,YAEA7U,WAAW,CAACE,IAFZ,IAEmB;AAChBmS,IAAAA,UAAU,EAAE;AADI,GAFnB,SAKArS,WAAW,CAACC,MALZ,IAKqB;AAClBkP,IAAAA,KAAK,EAAK8F,aAAL;AADa,GALrB,SAhDiB;AAyDtBO,EAAAA,MAAM;AACFzM,IAAAA,IAAI,EAAE,KADJ;AAEFX,IAAAA,SAAS,EAAE;AAFT,aAGDpI,WAAW,CAACE,IAHX,IAGkB;AAChBmS,IAAAA,UAAU,EAAE;AADI,GAHlB;AAzDgB,CAAD,CAAzB;;AAyEA,IAAMvE,iBAAiB,GAAqC,SAAtDA,iBAAsD,CAAC1M,KAAD;uBACAA,MAAhD+M;MAAAA,sCAAU;MAAI9B,eAAkCjL,MAAlCiL;MAAcyI,QAAoB1T,MAApB0T;MAAO1Q,WAAahD,MAAbgD;AAE3C,MAAMqR,iBAAiB,GAAG1G,IAAI,CAC1BlB,gBAAgB,CAACC,iBADS,EAE1BR,QAAM,CAACjB,YAAY,CAAC3L,QAAd,CAFoB,EAG1B4M,QAAM,CAACjB,YAAY,CAAC1L,UAAd,CAHoB,EAK1B2M,QAAM,CAACc,IALmB;AAM1BD,EAAAA,OAAO,CAACsB,aANkB,EAO1BtB,OAAO,2BAAyB3N,kBAAkB,CAAC6L,YAAD,CAA3C,CAPmB,EAIJyI,KAJI,IAIvBxH,QAAM,CAACgI,SAJgB,CAA9B;AAUA,SAAO1V,4BAAA,MAAA;AAAKkP,IAAAA,SAAS,EAAE2G;GAAhB,EAAoCrR,QAApC,CAAP;AACH,CAdD;;AAgBA,uCAAeiP,IAAI,CAACvF,iBAAD,CAAnB;;ACzGA;AACA,IAAM4H,OAAO,GAAGC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAAzC;;AAEA,IAAMC,QAAQ,GAAG;AACbC,EAAAA,cAAc,EACV;AAFS,CAAjB;AAKA,eAAe,UAACC,UAAD;AACX,MAAI,CAACN,OAAL,EAAc;AAEd,MAAM/C,OAAO,GAAGmD,QAAQ,CAACE,UAAD,CAAxB;;AACA,MAAI,OAAOC,OAAP,KAAmB,WAAvB,EAAoC;AAChCA,IAAAA,OAAO,CAACtK,KAAR,2BAAsCgH,OAAtC;AACH;;AACD,MAAI;AACA,UAAM,IAAIlO,KAAJ,CAAUkO,OAAV,CAAN;AACH,GAFD,CAEE,OAAOuD,CAAP,EAAU;AACf,CAVD;;ACaA,IAAMC,SAAS,GAAG,SAAZA,SAAY,CACdC,gBADc;AAGd,MAAMC,SAAS,GAAG,OAAOD,gBAAP,KAA4B,QAA5B,IAAwCE,cAAc,CAACF,gBAAD,CAAxE;AACA,SAAO,CAACC,SAAR;AACH,CALD;;AAgBA,IAAWvW,eAAJ;AACP,IAAWC,aAAJ;;IAEDwW;;;AACF,4BAAYnV,KAAZ;;;AACI,kCAAMA,KAAN;AAkBJ;;;;;AAIA,yBAAA,GAAkB,UACdgV,gBADc,EAEdI,eAFc;UAEdA;AAAAA,QAAAA,kBAAiC;;;AAEjC,UAAIJ,gBAAgB,KAAKjM,SAArB,IAAkCiM,gBAAgB,KAAK,IAA3D,EAAiE;AAC7D,cAAM,IAAI3R,KAAJ,CAAU,8CAAV,CAAN;AACH;;AAED,UAAMgS,IAAI,GAAGN,SAAS,CAACC,gBAAD,CAAT,GAA8BA,gBAA9B,GAAiDI,eAA9D;AAEA,UAAM7D,OAAO,GAAgCwD,SAAS,CAACC,gBAAD,CAAT,GACvCA,gBAAgB,CAACzD,OADsB,GAEvCyD,gBAFN;;UAIQzI,MAAsC8I,KAAtC9I;UAAK+I,mBAAiCD,KAAjCC;UAAqBnQ,wCAAYkQ;;AAE9C,UAAME,eAAe,GAAG/V,SAAS,CAAC+M,GAAD,CAAjC;AACA,UAAMmD,EAAE,GAAG6F,eAAe,GAAIhJ,GAAJ,GAA0B,IAAIiJ,IAAJ,GAAWC,OAAX,KAAuBzQ,IAAI,CAAC0Q,MAAL,EAA3E;AAEA,UAAMC,MAAM,GAAG7J,KAAK,CAAC3G,OAAD,EAAU,MAAKnF,KAAf,CAApB;;AACA,UAAMyS,KAAK;AACP/C,QAAAA,EAAE,EAAFA;AADO,SAEJvK,OAFI;AAGPoM,QAAAA,OAAO,EAAPA,OAHO;AAIP5B,QAAAA,IAAI,EAAE,IAJC;AAKP1C,QAAAA,OAAO,EAAE,KALF;AAMPyF,QAAAA,YAAY,EAAE,KANP;AAOP/H,QAAAA,OAAO,EAAEgL,MAAM,CAAC,SAAD,CAPR;AAQPnE,QAAAA,MAAM,EAAEmE,MAAM,CAAC,QAAD,CARP;AASPxC,QAAAA,OAAO,EAAEwC,MAAM,CAAC,SAAD,CATR;AAUP7K,QAAAA,OAAO,EAAE6K,MAAM,CAAC,SAAD,CAVR;AAWP1K,QAAAA,YAAY,EAAE0K,MAAM,CAAC,cAAD,CAXb;AAYP9K,QAAAA,yBAAyB,EAAE8K,MAAM,CAAC,2BAAD,CAZ1B;AAaP5K,QAAAA,gBAAgB,EAAE4K,MAAM,CAAC,kBAAD,CAbjB;AAcP/K,QAAAA,eAAe,EAAE+K,MAAM,CAAC,iBAAD,CAdhB;AAePxM,QAAAA,mBAAmB,EAAEwM,MAAM,CAAC,qBAAD,CAfpB;AAgBPzK,QAAAA,kBAAkB,EAAEyK,MAAM,CAAC,oBAAD,CAhBnB;AAiBP1C,QAAAA,eAAe,EAAE0C,MAAM,CAAC,iBAAD,EAAoB,IAApB,CAjBhB;AAkBP3K,QAAAA,WAAW,EAAE2K,MAAM,CAAC,aAAD,EAAgB,IAAhB,CAlBZ;AAmBP3R,QAAAA,KAAK,EAAE2R,MAAM,CAAC,OAAD,EAAU,IAAV,CAnBN;AAoBP/F,QAAAA,aAAa,EAAE+F,MAAM,CAAC,eAAD,EAAkB,IAAlB,CApBd;AAqBPjI,QAAAA,SAAS,EAAEC,IAAI,CAAC,MAAK3N,KAAL,CAAW0N,SAAZ,EAAuBvI,OAAO,CAACuI,SAA/B;AArBR,QAAX;;AAwBA,UAAI+E,KAAK,CAAC9H,OAAV,EAAmB;AACf8H,QAAAA,KAAK,CAAC1H,gBAAN,GAAyBhC,SAAzB;AACH;;AAED,YAAKrH,QAAL,CAAc,UAACpB,KAAD;AACV,YAAKgV,gBAAgB,KAAKvM,SAArB,IAAkC,MAAK/I,KAAL,CAAWsV,gBAA9C,IAAmEA,gBAAvE,EAAyF;AACrF,cAAMM,eAAe,GAAG,SAAlBA,eAAkB,CAAClK,IAAD;AAAA,mBACpB6J,eAAe,GAAG7J,IAAI,CAACgE,EAAL,KAAYA,EAAf,GAAoBhE,IAAI,CAAC6F,OAAL,KAAiBA,OADhC;AAAA,WAAxB;;AAGA,cAAMsE,OAAO,GAAGvV,KAAK,CAACwV,KAAN,CAAYC,SAAZ,CAAsBH,eAAtB,IAAyC,CAAC,CAA1D;AACA,cAAMI,MAAM,GAAG1V,KAAK,CAAC2V,MAAN,CAAaF,SAAb,CAAuBH,eAAvB,IAA0C,CAAC,CAA1D;;AACA,cAAIC,OAAO,IAAIG,MAAf,EAAuB;AACnB,mBAAO1V,KAAP;AACH;AACJ;;AAED,eAAO,MAAK4V,kBAAL,cACA5V,KADA;AAEHwV,UAAAA,KAAK,YAAMxV,KAAK,CAACwV,KAAZ,GAAmBrD,KAAnB;AAFF,WAAP;AAIH,OAhBD;;AAkBA,aAAO/C,EAAP;AACH,KAnED;AAqEA;;;;;;AAIA,4BAAA,GAA8B,UAACpP,KAAD;UAClB2V,SAAW3V,MAAX2V;;AACR,UAAIA,MAAM,CAACE,MAAP,IAAiB,MAAKzL,QAA1B,EAAoC;AAChC,eAAO,MAAK0L,mBAAL,CAAyB9V,KAAzB,CAAP;AACH;;AACD,aAAO,MAAK+V,YAAL,CAAkB/V,KAAlB,CAAP;AACH,KAND;AAQA;;;;;AAGA,sBAAA,GAAwB,UAACA,KAAD;UACZwV,QAAkBxV,MAAlBwV;UAAOG,SAAW3V,MAAX2V;;AACf,UAAIH,KAAK,CAACK,MAAN,GAAe,CAAnB,EAAsB;AAClB,4BACO7V,KADP;AAEI2V,UAAAA,MAAM,YAAMA,MAAN,GAAcH,KAAK,CAAC,CAAD,CAAnB,EAFV;AAGIA,UAAAA,KAAK,EAAEA,KAAK,CAAC3W,KAAN,CAAY,CAAZ,EAAe2W,KAAK,CAACK,MAArB;AAHX;AAKH;;AACD,aAAO7V,KAAP;AACH,KAVD;AAYA;;;;;;;;;;AAQA,6BAAA,GAA+B,UAACA,KAAD;AAC3B,UAAIA,KAAK,CAAC2V,MAAN,CAAarK,IAAb,CAAkB,UAACF,IAAD;AAAA,eAAU,CAACA,IAAI,CAACiE,IAAN,IAAcjE,IAAI,CAACgH,YAA7B;AAAA,OAAlB,CAAJ,EAAkE;AAC9D,eAAOpS,KAAP;AACH;;AAED,UAAIgW,MAAM,GAAG,KAAb;AACA,UAAIC,MAAM,GAAG,KAAb;AAEA,UAAMC,eAAe,GAAGlW,KAAK,CAAC2V,MAAN,CAAa5J,MAAb,CACpB,UAACC,GAAD,EAAMlJ,OAAN;AAAA,eAAkBkJ,GAAG,IAAIlJ,OAAO,CAACuM,IAAR,IAAgBvM,OAAO,CAACuH,OAAxB,GAAkC,CAAlC,GAAsC,CAA1C,CAArB;AAAA,OADoB,EAEpB,CAFoB,CAAxB;;AAKA,UAAI6L,eAAe,KAAK,MAAK9L,QAA7B,EAAuC;AACnC,gDAAAJ,OAAO,CAAC,gBAAD,CAAP;AACAiM,QAAAA,MAAM,GAAG,IAAT;AACH;;AAED,UAAMN,MAAM,GAAG3V,KAAK,CAAC2V,MAAN,CAAa1Q,GAAb,CAAiB,UAACmG,IAAD;AAC5B,YAAI,CAAC4K,MAAD,KAAY,CAAC5K,IAAI,CAACf,OAAN,IAAiB4L,MAA7B,CAAJ,EAA0C;AACtCD,UAAAA,MAAM,GAAG,IAAT;;AAEA,cAAI,CAAC5K,IAAI,CAACuB,OAAV,EAAmB;AACf,gCACOvB,IADP;AAEIgH,cAAAA,YAAY,EAAE;AAFlB;AAIH;;AAED,cAAIhH,IAAI,CAAC+D,OAAT,EAAkB;AACd/D,YAAAA,IAAI,CAAC+D,OAAL,CAAa,IAAb,EAAmB,UAAnB,EAA+B/D,IAAI,CAACgE,EAApC;AACH;;AAED,cAAI,MAAK1P,KAAL,CAAWyP,OAAf,EAAwB;AACpB,kBAAKzP,KAAL,CAAWyP,OAAX,CAAmB,IAAnB,EAAyB,UAAzB,EAAqC/D,IAAI,CAACgE,EAA1C;AACH;;AAED,8BACOhE,IADP;AAEIiE,YAAAA,IAAI,EAAE;AAFV;AAIH;;AAED,4BAAYjE,IAAZ;AACH,OA1Bc,CAAf;AA4BA,0BAAYpL,KAAZ;AAAmB2V,QAAAA,MAAM,EAANA;AAAnB;AACH,KA/CD;AAiDA;;;;;AAGA,4BAAA,GAA0D,UAAClU,IAAD,EAAOJ,WAAP,EAAoB4K,GAApB;AACtD,UAAI,CAAC/M,SAAS,CAAC+M,GAAD,CAAd,EAAqB;AACjB,cAAM,IAAIlJ,KAAJ,CAAU,wDAAV,CAAN;AACH;;AAED,YAAK3B,QAAL,CAAc;AAAA,YAAGuU,MAAH,QAAGA,MAAH;AAAA,eAAiB;AAC3BA,UAAAA,MAAM,EAAEA,MAAM,CAAC1Q,GAAP,CAAW,UAACmG,IAAD;AAAA,mBAAWA,IAAI,CAACgE,EAAL,KAAYnD,GAAZ,gBAAuBb,IAAvB;AAA6BuB,cAAAA,OAAO,EAAE;AAAtC,8BAAoDvB,IAApD,CAAX;AAAA,WAAX;AADmB,SAAjB;AAAA,OAAd;AAGH,KARD;AAUA;;;;;AAGA,0BAAA,GAAwD,UAAC0E,KAAD,EAAQqG,MAAR,EAAgBlK,GAAhB;AACpD;AACA;AACA,UAAI,MAAKvM,KAAL,CAAWyP,OAAf,EAAwB;AACpB,cAAKzP,KAAL,CAAWyP,OAAX,CAAmBW,KAAnB,EAA0BqG,MAA1B,EAAkClK,GAAlC;AACH;;AAED,UAAMmK,cAAc,GAAGnK,GAAG,KAAKxD,SAA/B;;AAEA,YAAKrH,QAAL,CAAc;AAAA,YAAGuU,MAAH,SAAGA,MAAH;AAAA,YAAWH,KAAX,SAAWA,KAAX;AAAA,eAAwB;AAClCG,UAAAA,MAAM,EAAEA,MAAM,CAAC1Q,GAAP,CAAW,UAACmG,IAAD;AACf,gBAAI,CAACgL,cAAD,IAAmBhL,IAAI,CAACgE,EAAL,KAAYnD,GAAnC,EAAwC;AACpC,kCAAYb,IAAZ;AACH;;AAED,mBAAOA,IAAI,CAACuB,OAAL,gBAAoBvB,IAApB;AAA0BiE,cAAAA,IAAI,EAAE;AAAhC,8BAA+CjE,IAA/C;AAAqDgH,cAAAA,YAAY,EAAE;AAAnE,cAAP;AACH,WANO,CAD0B;AAQlCoD,UAAAA,KAAK,EAAEA,KAAK,CAACjH,MAAN,CAAa,UAACnD,IAAD;AAAA,mBAAUA,IAAI,CAACgE,EAAL,KAAYnD,GAAtB;AAAA,WAAb;AAR2B,SAAxB;AAAA,OAAd;AAUH,KAnBD;AAqBA;;;;;AAGA,uBAAA,GAAkD,UAACA,GAAD;AAC9C;AACA,UAAMoK,UAAU,GAAG,MAAKrW,KAAL,CAAW2V,MAAX,CAAkBW,IAAlB,CAAuB,UAAClL,IAAD;AAAA,eAAUA,IAAI,CAACgE,EAAL,KAAYnD,GAAtB;AAAA,OAAvB,CAAnB;;AACA,UAAI/M,SAAS,CAAC+M,GAAD,CAAT,IAAkBoK,UAAlB,IAAgCA,UAAU,CAAClH,OAA/C,EAAwD;AACpDkH,QAAAA,UAAU,CAAClH,OAAX,CAAmB,IAAnB,EAAyB,YAAzB,EAAuClD,GAAvC;AACH;;AAED,YAAKsK,gBAAL,CAAsB,IAAtB,EAA4B,YAA5B,EAA0CtK,GAA1C;AACH,KARD;AAUA;;;;;;;;;AAOA,2BAAA,GAAwD,UAACxK,IAAD,EAAOwK,GAAP;AACpD,UAAI,CAAC/M,SAAS,CAAC+M,GAAD,CAAd,EAAqB;AACjB,cAAM,IAAIlJ,KAAJ,CAAU,uDAAV,CAAN;AACH;;AAED,YAAK3B,QAAL,CAAc,UAACpB,KAAD;AACV,YAAMwW,QAAQ,GAAG,MAAKT,YAAL,cACV/V,KADU;AAEb2V,UAAAA,MAAM,EAAE3V,KAAK,CAAC2V,MAAN,CAAapH,MAAb,CAAoB,UAACnD,IAAD;AAAA,mBAAUA,IAAI,CAACgE,EAAL,KAAYnD,GAAtB;AAAA,WAApB;AAFK,WAAjB;;AAKA,YAAIuK,QAAQ,CAAChB,KAAT,CAAeK,MAAf,KAA0B,CAA9B,EAAiC;AAC7B,iBAAOW,QAAP;AACH;;AAED,eAAO,MAAKV,mBAAL,CAAyBU,QAAzB,CAAP;AACH,OAXD;AAYH,KAjBD;;AAvOIpY,IAAAA,eAAe,GAAG,MAAKA,eAAvB;AACAC,IAAAA,aAAa,GAAG,MAAKA,aAArB;AAEA,UAAK2B,KAAL,GAAa;AACT2V,MAAAA,MAAM,EAAE,EADC;AAETH,MAAAA,KAAK,EAAE,EAFE;AAGTiB,MAAAA,YAAY,EAAE;AACVrY,QAAAA,eAAe,EAAE,MAAKA,eAAL,CAAqBsY,IAArB,+BADP;AAEVrY,QAAAA,aAAa,EAAE,MAAKA,aAAL,CAAmBqY,IAAnB;AAFL;AAHL,KAAb;;AAQH;;;;SA+ODjU,SAAA;;;QACYgU,eAAiB,KAAKzW,MAAtByW;sBAC+D,KAAK/W;QAApEiX,sBAAAA;QAASjU,uBAAAA;wCAAU0Q;QAAAA,uCAAQ;4CAAOwD;QAAAA,gDAAa;QAAInK,sBAAAA;AAE3D,QAAMoK,KAAK,GAAG,KAAK7W,KAAL,CAAW2V,MAAX,CAAkB5J,MAAlB,CAA2C,UAACC,GAAD,EAAMlJ,OAAN;;;AACrD,UAAMgU,QAAQ,GAAGhY,kBAAkB,CAACgE,OAAO,CAAC6H,YAAT,CAAnC;AACA,UAAMoM,kBAAkB,GAAG/K,GAAG,CAAC8K,QAAD,CAAH,IAAiB,EAA5C;AACA,0BACO9K,GADP,6BAEK8K,QAFL,cAEoBC,kBAFpB,GAEwCjU,OAFxC;AAIH,KAPa,EAOX,EAPW,CAAd;AASA,QAAMkU,SAAS,GAAGnL,MAAM,CAACyC,IAAP,CAAYuI,KAAZ,EAAmB5R,GAAnB,CAAuB,UAACgS,MAAD;AACrC,UAAMtB,MAAM,GAAGkB,KAAK,CAACI,MAAD,CAApB;UACOC,eAAgBvB;AACvB,aACIzX,4BAAA,CAACkO,mBAAD;AACIH,QAAAA,GAAG,EAAEgL;AACL7D,QAAAA,KAAK,EAAEA;AACPzI,QAAAA,YAAY,EAAEuM,YAAY,CAACvM;AAC3B8B,QAAAA,OAAO,EAAEA;OAJb,EAMKkJ,MAAM,CAAC1Q,GAAP,CAAW,UAACkN,KAAD;AAAA,eACRjU,4BAAA,CAAC4T,YAAD;AACI7F,UAAAA,GAAG,EAAEkG,KAAK,CAAC/C;AACX+C,UAAAA,KAAK,EAAEA;AACP1F,UAAAA,OAAO,EAAEA;AACTzJ,UAAAA,SAAS,EAAE4T,UAAU,CAACzE,KAAK,CAAC3H,OAAP;AACrB2E,UAAAA,OAAO,EAAE,MAAI,CAACoH;AACd7U,UAAAA,OAAO,EAAE,MAAI,CAAChC,KAAL,CAAWgC;AACpBI,UAAAA,MAAM,EAAE,MAAI,CAACpC,KAAL,CAAWoC;AACnBD,UAAAA,QAAQ,EAAE4M,qBAAqB,CAAC,CAAC,MAAI,CAAC0I,iBAAN,EAAyB,MAAI,CAACzX,KAAL,CAAWmC,QAApC,CAAD,EAAgDsQ,KAAK,CAAC/C,EAAtD;AAC/B5N,UAAAA,SAAS,EAAEiN,qBAAqB,CAAC,CAAC,MAAI,CAAC2I,kBAAN,EAA0B,MAAI,CAAC1X,KAAL,CAAW8B,SAArC,CAAD,EAAkD2Q,KAAK,CAAC/C,EAAxD;SATpC,CADQ;AAAA,OAAX,CANL,CADJ;AAsBH,KAzBiB,CAAlB;AA2BA,WACIlR,4BAAA,CAACmZ,eAAe,CAACC,QAAjB;AAA0BnY,MAAAA,KAAK,EAAEsX;KAAjC,EACK/T,QADL,EAEKiU,OAAO,GAAGY,YAAY,CAACP,SAAD,EAAYL,OAAZ,CAAf,GAAsCK,SAFlD,CADJ;AAMH;;;;;AA1RG,aAAO,KAAKtX,KAAL,CAAW0K,QAAX,IAAuBD,QAAQ,CAACC,QAAvC;AACH;;;;EAlB0BpH;;ACpC/B,mBAAe;AAAA,SAAuBwU,UAAU,CAACH,eAAD,CAAjC;AAAA,CAAf;;;;"}