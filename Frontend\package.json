{"name": "tailadmin-react", "private": true, "version": "2.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "11.11.3", "@emotion/styled": "11.11.0", "@fullcalendar/core": "6.1.10", "@fullcalendar/daygrid": "^6.1.10", "@fullcalendar/interaction": "^6.1.10", "@fullcalendar/react": "^6.1.10", "@fullcalendar/timegrid": "^6.1.10", "@headlessui/react": "1.7.18", "@heroicons/react": "2.1.1", "@hookform/resolvers": "3.3.4", "@mui/icons-material": "5.15.10", "@mui/material": "5.15.10", "@reduxjs/toolkit": "2.1.0", "@tailwindcss/forms": "0.5.7", "apexcharts": "3.45.2", "axios": "^1.9.0", "classnames": "2.5.1", "clsx": "2.1.0", "date-fns": "3.3.1", "file-saver": "^2.0.5", "flatpickr": "^4.6.13", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.511.0", "react": "18.2.0", "react-apexcharts": "^1.4.1", "react-dom": "18.2.0", "react-dropzone": "^14.2.3", "react-flatpickr": "^3.10.13", "react-helmet-async": "2.0.4", "react-hook-form": "7.50.1", "react-icons": "^5.5.0", "react-redux": "9.1.0", "react-router": "6.22.1", "react-router-dom": "6.22.1", "react-toastify": "10.0.4", "reactflow": "^11.10.3", "recharts": "^2.15.3", "redux": "5.0.1", "redux-persist": "6.0.0", "tailwind-merge": "2.2.1", "xlsx": "^0.18.5", "yup": "1.3.3"}, "devDependencies": {"@eslint/js": "8.56.0", "@tailwindcss/typography": "0.5.10", "@testing-library/react": "14.2.1", "@types/file-saver": "2.0.7", "@types/node": "20.11.19", "@types/react": "18.2.57", "@types/react-dom": "18.2.19", "@types/react-flatpickr": "3.8.11", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "10.4.17", "eslint": "8.56.0", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-refresh": "0.4.5", "globals": "13.24.0", "postcss": "8.4.35", "tailwindcss": "3.4.1", "typescript": "5.3.3", "typescript-eslint": "7.0.1", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0", "vitest": "^3.1.4"}}