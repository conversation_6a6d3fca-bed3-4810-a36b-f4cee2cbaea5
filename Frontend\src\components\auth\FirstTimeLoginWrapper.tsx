import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { CircularProgress, Box, Typography } from '@mui/material';
import { firstLoginService } from '../../services/firstLoginService.ts';
import { authService } from '../../services/authService.ts';
import { FirstLoginProps } from '../../types/auth';

/**
 * This component wraps the application and checks if the user is logging in for the first time.
 * If it is their first login, they are redirected to complete their profile.
 */
const FirstTimeLoginWrapper: React.FC<FirstLoginProps> = ({ children }) => {
  const [checking, setChecking] = useState(true);
  const [isFirstLogin, setIsFirstLogin] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Skip first login check for certain paths
  const skipPaths = [
    '/complete-profile',
    '/accept-invitation',
    '/register-qr',
    '/signin',
    '/register'
  ];

  const shouldSkipCheck = () => {
    return skipPaths.some(path => location.pathname.includes(path));
  };

  useEffect(() => {
    const checkFirstLogin = async () => {
      // Skip check if on excluded paths
      if (shouldSkipCheck()) {
        setChecking(false);
        return;
      }

      try {
        // Check if user is authenticated
        const isAuthenticated = authService.isAuthenticated();

        if (!isAuthenticated) {
          setChecking(false);
          return;
        }

        // Check if this is the first login
        const firstLogin = await firstLoginService.isFirstLogin();
        setIsFirstLogin(firstLogin);

        if (firstLogin) {
          // Get current user data to pass to the profile completion page
          try {
            const userData = await authService.getCurrentUser();

            // Only redirect if we have valid user data
            if (userData && userData.id) {
              // Redirect to profile completion with user data
              navigate('/complete-profile', {
                state: {
                  userId: userData.id,
                  userType: userData.user_type,
                  isFirstLogin: true,
                  returnUrl: location.pathname
                }
              });
            } else {
              console.warn('No valid user data found for first login redirect');
              setChecking(false);
            }
          } catch (userError) {
            console.error('Error getting user data for first login:', userError);
            // If we can't get user data, try to refresh it
            try {
              const refreshedUserData = await authService.refreshUserData();
              if (refreshedUserData && refreshedUserData.id) {
                navigate('/complete-profile', {
                  state: {
                    userId: refreshedUserData.id,
                    userType: refreshedUserData.user_type,
                    isFirstLogin: true,
                    returnUrl: location.pathname
                  }
                });
              } else {
                setChecking(false);
              }
            } catch (refreshError) {
              console.error('Error refreshing user data:', refreshError);
              setChecking(false);
            }
          }
        } else {
          setChecking(false);
        }
      } catch (error) {
        console.error('Error checking first login:', error);
        setChecking(false);
      }
    };

    checkFirstLogin();
  }, [navigate, location.pathname]);

  if (checking) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100vh' }}>
        <CircularProgress />
        <Typography variant="body1" sx={{ mt: 2 }}>
          Loading your profile...
        </Typography>
      </Box>
    );
  }

  // If it's not a first login or we're already on the profile completion page, render children
  return children;
};

export default FirstTimeLoginWrapper;
