import { useState, useEffect } from "react";
import { useNavigate, useLocation, Link as RouterLink } from "react-router-dom";
import { ChevronLeftIcon, EyeCloseIcon, EyeIcon } from "../../icons";
import { useAuth } from "../../context/AuthContext";
import { getRoleBasedHomePath } from "../../utils/routeUtils";
import Label from "../form/Label";
import Input from "../form/input/InputField";
import Checkbox from "../form/input/Checkbox";
import Button from "../ui/button/Button";
import { toast } from "react-toastify";

export default function SignInForm() {
  const [credentials, setCredentials] = useState({
    email: "",
    password: ""
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [animateForm, setAnimateForm] = useState(false);

  const navigate = useNavigate();
  const location = useLocation();
  const { login } = useAuth();

  // Add animation effect when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimateForm(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setCredentials({ ...credentials, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      const result = await login({
        email: credentials.email,
        password: credentials.password
      });

      // Check profile status and show appropriate messages
      if (result.profile_status === 'missing') {
        // Show profile missing warning notification
        toast.warning(result.message || 'Your profile needs to be created. Please contact your administrator to set up your profile with school and branch information.');
      } else if (result.profile_status === 'error') {
        // Show profile error notification
        toast.error(result.message || 'There was an issue checking your profile. Please contact support if you experience any issues.');
      } else if (result.profile_status === 'not_required') {
        // Show info for superusers
        toast.info(result.message || 'Welcome! You have superuser access.');
      } else if (result.profile_status === 'complete') {
        // Show success for users with complete profiles
        toast.success(result.message || 'Login successful. Welcome back!');
      }

      // Get the appropriate redirect path based on user role
      const redirectPath = getRoleBasedHomePath(result.user);

      // Check if we have a saved location to redirect to
      const savedLocation = location.state?.from;

      if (savedLocation && savedLocation.pathname !== '/signin' && savedLocation.pathname !== '/') {
        // Redirect to the saved location if it exists and is not the signin or root page
        navigate(savedLocation, { replace: true });
      } else {
        // Otherwise, redirect to the role-based dashboard
        navigate(redirectPath, { replace: true });
      }

    } catch (err: any) {
      // Handle license errors specifically
      if (err.response?.status === 403 && err.response?.data?.error) {
        setError(err.response.data.error);
      } else {
        setError(
          err.response?.data?.error ||
          err.response?.data?.detail ||
          err.message ||
          'An error occurred during login'
        );
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`flex flex-col flex-1 transition-all duration-500 ${animateForm ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
      <div className="w-full max-w-md pt-6 mx-auto">
        <button
          type="button"
          onClick={() => navigate("/")}
          className="inline-flex items-center text-sm text-gray-500 transition-colors hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
        >
          <ChevronLeftIcon className="size-5" />
          Back to dashboard
        </button>
      </div>

      <div className="flex flex-col justify-center flex-1 w-full max-w-md mx-auto">
        <div className="mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/30">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
              </svg>
            </div>
          </div>
          <h1 className="text-center mb-2 font-bold text-gray-800 text-2xl dark:text-white/90 sm:text-3xl">
            Welcome Back
          </h1>
          <p className="text-center text-sm text-gray-500 dark:text-gray-400">
            Sign in to your ShuleXcel account to continue
          </p>
        </div>

        {error && (
          <div className="p-3 mb-6 text-sm text-red-500 bg-red-100 dark:bg-red-900/20 rounded-lg shadow-sm">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="bg-white dark:bg-gray-800/50 p-6 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
          <div className="space-y-5">
            <div>
              <Label>
                Email <span className="text-error-500">*</span>
              </Label>
              <Input
                type="email"
                name="email"
                placeholder="<EMAIL>"
                value={credentials.email}
                onChange={handleChange}
                required
                className="focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <Label>
                Password <span className="text-error-500">*</span>
              </Label>
              <div className="relative">
                <Input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  placeholder="Enter your password"
                  value={credentials.password}
                  onChange={handleChange}
                  required
                  className="focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <span
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute z-30 -translate-y-1/2 cursor-pointer right-4 top-1/2"
                >
                  {showPassword ? (
                    <EyeIcon className="fill-gray-500 dark:fill-gray-400 size-5" />
                  ) : (
                    <EyeCloseIcon className="fill-gray-500 dark:fill-gray-400 size-5" />
                  )}
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Checkbox checked={isChecked} onChange={setIsChecked} />
                <span className="block font-normal text-gray-700 text-theme-sm dark:text-gray-400">
                  Keep me logged in
                </span>
              </div>
              <RouterLink to="/forgot-password" className="text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 hover:underline">
                Forgot password?
              </RouterLink>
            </div>

            <Button
              className="w-full py-2.5 bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700 transition-all duration-200"
              size="sm"
              type="submit"
              disabled={isLoading}
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Signing in...
                </span>
              ) : "Sign in"}
            </Button>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Contact your administrator if you need access to the system
              </p>
              <div className="flex items-center justify-center mt-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  Secure connection | All data is encrypted
                </p>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
