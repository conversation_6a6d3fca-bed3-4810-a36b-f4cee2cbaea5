import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import { authService } from "../../services/authService";
import { toast } from "react-toastify";
import Button from "../ui/button/Button";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import { UserIcon, CameraIcon } from "@heroicons/react/24/outline";

interface ProfileFormData {
  first_name: string;
  last_name: string;
  phone: string;
  profile_picture: File | null;
  // Additional fields based on user type
  date_of_birth?: string;
  address?: string;
  emergency_contact?: string;
  emergency_phone?: string;
}

interface ProfileCompletionFormProps {
  userType?: string;
  userId?: string;
  onComplete?: () => void;
  isFirstLogin?: boolean;
}

export default function ProfileCompletionForm({ 
  userType, 
  userId, 
  onComplete, 
  isFirstLogin = false 
}: ProfileCompletionFormProps) {
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [formData, setFormData] = useState<ProfileFormData>({
    first_name: user?.first_name || '',
    last_name: user?.last_name || '',
    phone: user?.phone || '',
    profile_picture: null,
    date_of_birth: '',
    address: '',
    emergency_contact: '',
    emergency_phone: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string>('');

  // Get user type from props or user object
  const currentUserType = userType || user?.user_type || '';

  useEffect(() => {
    // Pre-fill form with existing user data
    if (user) {
      setFormData(prev => ({
        ...prev,
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        phone: user.phone || ''
      }));
    }
  }, [user]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setErrors(prev => ({ ...prev, profile_picture: 'File size must be less than 5MB' }));
        return;
      }

      // Validate file type
      if (!file.type.startsWith('image/')) {
        setErrors(prev => ({ ...prev, profile_picture: 'Please select an image file' }));
        return;
      }

      setFormData(prev => ({ ...prev, profile_picture: file }));
      
      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);

      // Clear any previous error
      setErrors(prev => ({ ...prev, profile_picture: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields
    if (!formData.first_name.trim()) {
      newErrors.first_name = 'First name is required';
    }
    if (!formData.last_name.trim()) {
      newErrors.last_name = 'Last name is required';
    }

    // Phone validation (optional but if provided, must be valid)
    if (formData.phone && !/^[0-9+\-\s()]{7,15}$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    // Date of birth validation for students
    if (currentUserType === 'student' && !formData.date_of_birth) {
      newErrors.date_of_birth = 'Date of birth is required for students';
    }

    // Emergency contact for students and staff
    if (['student', 'staff'].includes(currentUserType)) {
      if (!formData.emergency_contact?.trim()) {
        newErrors.emergency_contact = 'Emergency contact name is required';
      }
      if (!formData.emergency_phone?.trim()) {
        newErrors.emergency_phone = 'Emergency contact phone is required';
      } else if (!/^[0-9+\-\s()]{7,15}$/.test(formData.emergency_phone)) {
        newErrors.emergency_phone = 'Please enter a valid emergency contact phone';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the errors in the form before submitting.');
      return;
    }

    setIsSubmitting(true);

    try {
      // Create FormData for file upload
      const formDataToSend = new FormData();
      
      // Add all form fields
      Object.entries(formData).forEach(([key, value]) => {
        if (value !== null && value !== '') {
          if (key === 'profile_picture' && value instanceof File) {
            formDataToSend.append(key, value);
          } else if (typeof value === 'string') {
            formDataToSend.append(key, value);
          }
        }
      });

      // Add user type for backend processing
      if (currentUserType) {
        formDataToSend.append('user_type', currentUserType);
      }

      let response;
      if (isFirstLogin || !userId) {
        // Complete current user's profile
        response = await authService.completeProfile(formDataToSend);
      } else {
        // Complete registration for specific user
        response = await authService.registerStepTwo(userId, formDataToSend);
      }

      toast.success(
        isFirstLogin 
          ? 'Profile updated successfully!' 
          : 'Profile completed successfully!'
      );

      // Call completion callback or navigate
      setTimeout(() => {
        if (onComplete) {
          onComplete();
        } else {
          navigate('/dashboard');
        }
      }, 1500);

    } catch (error: any) {
      console.error('Profile completion error:', error);
      
      let errorMessage = 'Profile completion failed. Please try again.';
      if (error.response?.data) {
        const apiErrors = error.response.data;
        if (typeof apiErrors === 'object' && apiErrors.error) {
          errorMessage = apiErrors.error;
        } else if (typeof apiErrors === 'string') {
          errorMessage = apiErrors;
        }
      }
      
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getUserTypeTitle = () => {
    switch (currentUserType) {
      case 'student': return 'Student';
      case 'teacher': return 'Teacher';
      case 'parent': return 'Parent';
      case 'staff': return 'Staff';
      case 'admin': return 'Administrator';
      default: return 'User';
    }
  };

  const getUserTypeDescription = () => {
    switch (currentUserType) {
      case 'student': return 'Complete your student profile to access your academic dashboard';
      case 'teacher': return 'Complete your teacher profile to access teaching tools and resources';
      case 'parent': return 'Complete your parent profile to monitor your child\'s progress';
      case 'staff': return 'Complete your staff profile to access administrative tools';
      case 'admin': return 'Complete your administrator profile to access system management';
      default: return 'Complete your profile to access the system';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
            <UserIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-bold text-gray-900 dark:text-white">
          Complete Your {getUserTypeTitle()} Profile
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
          {getUserTypeDescription()}
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Profile Picture Upload */}
            <div className="flex flex-col items-center">
              <div className="relative">
                <div className="w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center overflow-hidden">
                  {previewUrl ? (
                    <img 
                      src={previewUrl} 
                      alt="Profile preview" 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <UserIcon className="h-12 w-12 text-gray-400" />
                  )}
                </div>
                <label 
                  htmlFor="profile-picture" 
                  className="absolute bottom-0 right-0 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-2 cursor-pointer transition-colors"
                >
                  <CameraIcon className="h-4 w-4" />
                </label>
                <input
                  id="profile-picture"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="hidden"
                />
              </div>
              {errors.profile_picture && (
                <p className="mt-1 text-sm text-red-600">{errors.profile_picture}</p>
              )}
              <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                Upload a profile picture (optional, max 5MB)
              </p>
            </div>

            {/* Basic Information */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <Label htmlFor="first_name">
                  First Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="first_name"
                  name="first_name"
                  type="text"
                  value={formData.first_name}
                  onChange={handleInputChange}
                  className={errors.first_name ? 'border-red-500' : ''}
                  required
                />
                {errors.first_name && (
                  <p className="mt-1 text-sm text-red-600">{errors.first_name}</p>
                )}
              </div>

              <div>
                <Label htmlFor="last_name">
                  Last Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="last_name"
                  name="last_name"
                  type="text"
                  value={formData.last_name}
                  onChange={handleInputChange}
                  className={errors.last_name ? 'border-red-500' : ''}
                  required
                />
                {errors.last_name && (
                  <p className="mt-1 text-sm text-red-600">{errors.last_name}</p>
                )}
              </div>
            </div>

            {/* Phone Number */}
            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleInputChange}
                placeholder="+254 700 000 000"
                className={errors.phone ? 'border-red-500' : ''}
              />
              {errors.phone && (
                <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
              )}
            </div>

            {/* Student-specific fields */}
            {currentUserType === 'student' && (
              <>
                <div>
                  <Label htmlFor="date_of_birth">
                    Date of Birth <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="date_of_birth"
                    name="date_of_birth"
                    type="date"
                    value={formData.date_of_birth}
                    onChange={handleInputChange}
                    className={errors.date_of_birth ? 'border-red-500' : ''}
                    required
                  />
                  {errors.date_of_birth && (
                    <p className="mt-1 text-sm text-red-600">{errors.date_of_birth}</p>
                  )}
                </div>
              </>
            )}

            {/* Emergency Contact for Students and Staff */}
            {['student', 'staff'].includes(currentUserType) && (
              <>
                <div>
                  <Label htmlFor="emergency_contact">
                    Emergency Contact Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="emergency_contact"
                    name="emergency_contact"
                    type="text"
                    value={formData.emergency_contact}
                    onChange={handleInputChange}
                    placeholder="Full name of emergency contact"
                    className={errors.emergency_contact ? 'border-red-500' : ''}
                    required
                  />
                  {errors.emergency_contact && (
                    <p className="mt-1 text-sm text-red-600">{errors.emergency_contact}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="emergency_phone">
                    Emergency Contact Phone <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="emergency_phone"
                    name="emergency_phone"
                    type="tel"
                    value={formData.emergency_phone}
                    onChange={handleInputChange}
                    placeholder="+254 700 000 000"
                    className={errors.emergency_phone ? 'border-red-500' : ''}
                    required
                  />
                  {errors.emergency_phone && (
                    <p className="mt-1 text-sm text-red-600">{errors.emergency_phone}</p>
                  )}
                </div>
              </>
            )}

            {/* Address for all user types */}
            <div>
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                name="address"
                type="text"
                value={formData.address}
                onChange={handleInputChange}
                placeholder="Your current address"
              />
            </div>

            {/* Submit Button */}
            <div>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Completing Profile...
                  </div>
                ) : (
                  'Complete Profile'
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
