import React, { useState } from 'react';
import ImageCropper from '../ui/ImageCropper';

// Custom hooks
import useFormValidation from './hooks/useFormValidation';
import useSchoolData from './hooks/useSchoolData';
import useFileHandling from './hooks/useFileHandling';
import useUserCreation from './hooks/useUserCreation';

// Components
import StepProgressIndicator from './components/StepProgressIndicator';
import NotificationAlert from './components/NotificationAlert';
import SchoolBranchSelector from './components/SchoolBranchSelector';
import BasicInformationForm from './components/BasicInformationForm';
import UserTypeSelector from './components/UserTypeSelector';
import ProfileInformationForm from './components/ProfileInformationForm';
import ConfirmationDialog from './components/ConfirmationDialog';
import CreatedUserSuccess from './components/CreatedUserSuccess';

// Role-specific forms
import { <PERSON>F<PERSON>, TeacherForm, StaffForm, ParentForm } from './components/RoleSpecificForms';

// Interfaces
import { UserFormData, CreatedUser, NotificationState } from './interfaces/UserInterfaces';

// User type categories
const userTypeCategories = [
  {
    id: 'system',
    name: 'System Level',
    description: 'Users with system-wide access and permissions',
    types: [
      { value: 'system_admin', label: 'System Administrator', icon: '🔧', description: 'Full access to all system features and settings' }
    ]
  },
  {
    id: 'school_admin',
    name: 'School Administration',
    description: 'School leadership and administrative roles',
    types: [
      { value: 'school_admin', label: 'School Administrator', icon: '👨‍💼', description: 'Principal/Head of school with full school access' },
      { value: 'deputy_principal', label: 'Deputy Principal', icon: '👩‍💼', description: 'Assists the principal in school management' },
      { value: 'branch_admin', label: 'Branch Administrator', icon: '🏫', description: 'Manages a specific branch of the school' },
      { value: 'department_head', label: 'Department Head', icon: '📚', description: 'Leads an academic department (HOD)' },
      { value: 'ict_admin', label: 'ICT Administrator', icon: '💻', description: 'Manages school technology and systems' }
    ]
  },
  {
    id: 'academic',
    name: 'Academic Staff',
    description: 'Teaching and academic support roles',
    types: [
      { value: 'teacher', label: 'Teacher', icon: '👨‍🏫', description: 'Classroom instructor for specific subjects' },
      { value: 'librarian', label: 'Librarian', icon: '📖', description: 'Manages the school library and resources' },
      { value: 'counselor', label: 'School Counselor', icon: '🧠', description: 'Provides guidance and counseling to students' }
    ]
  },
  {
    id: 'support',
    name: 'Support Staff',
    description: 'Non-teaching staff supporting school operations',
    types: [
      { value: 'accountant', label: 'Accountant', icon: '💰', description: 'Manages school finances and accounts' },
      { value: 'secretary', label: 'Secretary', icon: '📝', description: 'Administrative support for school leadership' },
      { value: 'nurse', label: 'School Nurse', icon: '🩺', description: 'Provides healthcare services to students' },
      { value: 'maintenance', label: 'Maintenance Staff', icon: '🔨', description: 'Maintains school facilities and grounds' },
      { value: 'security', label: 'Security Staff', icon: '🔒', description: 'Ensures safety and security on campus' },
      { value: 'driver', label: 'Driver', icon: '🚌', description: 'Operates school transportation vehicles' }
    ]
  },
  {
    id: 'students_parents',
    name: 'Students and Parents',
    description: 'Primary users of the school system',
    types: [
      { value: 'student', label: 'Student', icon: '👨‍🎓', description: 'Enrolled learner at the school' },
      { value: 'parent', label: 'Parent/Guardian', icon: '👪', description: 'Parent or legal guardian of a student' }
    ]
  }
];

interface CreateUserFormProps {
  onUserCreated?: (user: CreatedUser) => void;
}

const CreateUserForm: React.FC<CreateUserFormProps> = ({ onUserCreated }) => {
  // Form data state
  const [formData, setFormData] = useState<UserFormData>({
    email: '',
    first_name: '',
    last_name: '',
    phone_number: '',
    user_type: '',
    school: '',
    school_branch: '',
    generate_password: true,

    // Initialize profile fields
    gender: '',
    date_of_birth: '',
    address: '',
    profile_picture: null,
    profile_picture_preview: '',

    // Student fields
    admission_number: '',
    class_name: '',
    stream: '',
    birth_certificate: null,
    birth_certificate_preview: '',

    // Teacher fields
    teacher_number: '',
    department: '',
    is_class_teacher: false,
    assigned_classes: [],
    cv_document: null,
    cv_document_preview: '',

    // Staff fields
    staff_number: '',
    role: '',

    // Parent fields
    relationship: '',
    children: []
  });

  // UI state
  const [notification, setNotification] = useState<NotificationState>({
    open: false,
    message: '',
    type: 'info'
  });

  // Form step state (for multi-step form)
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 5; // Total number of steps in the form (added confirmation step)

  // Step titles
  const stepTitles = [
    "School & Branch Selection",
    "Basic Information",
    "User Type & Role",
    "Role-Specific Information",
    "Review & Confirm"
  ];

  // Custom hooks
  const { errors, setErrors, validateForm } = useFormValidation();

  const {
    schools, branches, departments, subjects, classes, streams, roles, students,
    isLoadingSchools, isLoadingBranches, isLoadingDepartments, isLoadingSubjects,
    isLoadingClasses, isLoadingStreams, isLoadingRoles, isLoadingStudents,
    handleSchoolChange, handleBranchChange, handleClassChange, handleChildrenChange
  } = useSchoolData(formData, setFormData, setNotification);

  const {
    cropperState, handleFileChange, handleRemoveFile, handleCropComplete, handleCropCancel
  } = useFileHandling(formData, setFormData, setNotification);

  const {
    isSubmitting, createdUser, showConfirmation, setShowConfirmation,
    handleReset, handleConfirmationClose, handleConfirmationConfirm, handlePrintCredentials
  } = useUserCreation(formData, setFormData, setNotification, onUserCreated);

  // Handle form field changes with real-time validation
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error for this field
    setErrors(prev => ({ ...prev, [name]: '' }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Handle multi-select changes
  const handleMultiSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name } = e.target;
    const selectedOptions = Array.from(e.target.selectedOptions).map(option => Number(option.value));
    setFormData(prev => ({ ...prev, [name]: selectedOptions }));
  };

  // Handle field blur for validation
  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Perform basic validation
    if (!value && ['email', 'first_name', 'last_name', 'phone_number', 'user_type', 'school', 'school_branch'].includes(name)) {
      setErrors(prev => ({ ...prev, [name]: `${name.replace('_', ' ')} is required` }));
    } else {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Handle file selection for specific fields
  const handleFileSelect = (fieldName: string) => (file: File) => {
    const reader = new FileReader();
    reader.onloadend = () => {
      setFormData(prev => ({
        ...prev,
        [fieldName]: file,
        [`${fieldName}_preview`]: reader.result as string
      }));
    };
    reader.readAsDataURL(file);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm(formData, currentStep)) {
      setNotification({
        open: true,
        message: 'Please fix the errors in the form before submitting.',
        type: 'error'
      });
      return;
    }

    // Check if school_branch is selected
    if (!formData.school_branch && branches && branches.length > 0) {
      // Auto-select the first branch if none is selected
      setFormData(prev => ({
        ...prev,
        school_branch: branches[0].id
      }));
    }

    // If we're on the final review step, show confirmation dialog
    if (currentStep === totalSteps) {
      setShowConfirmation(true);
      return;
    }

    // If not on the final step, move to the next step
    if (currentStep < totalSteps) {
      goToNextStep();
      return;
    }
  };

  // Handle notification close
  const handleNotificationClose = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  // Step navigation functions
  const goToNextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
      window.scrollTo(0, 0);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      window.scrollTo(0, 0);
    }
  };

  // Validate current step before proceeding
  const validateCurrentStep = (): boolean => {
    return validateForm(formData, currentStep);
  };

  // Handle next button click with validation
  const handleNextStep = () => {
    if (validateCurrentStep()) {
      goToNextStep();
    } else {
      setNotification({
        open: true,
        message: 'Please fix the errors before proceeding to the next step.',
        type: 'error'
      });
    }
  };

  // Handle user type selection
  const handleUserTypeChange = (userType: string) => {
    setFormData(prev => ({ ...prev, user_type: userType }));
  };

  return (
    <>
      {/* Image Cropper */}
      {cropperState.isOpen && (
        <ImageCropper
          imageUrl={cropperState.imageUrl}
          onCropComplete={handleCropComplete}
          onCancel={handleCropCancel}
          aspectRatio={cropperState.fieldName === 'profile_picture' ? 1 : 16/9} // 1:1 for profile pictures, 16:9 for documents
          minZoom={0.5}
          maxZoom={3}
          isDocument={cropperState.isDocument}
          documentName={cropperState.documentName}
        />
      )}

      {/* Notification */}
      <NotificationAlert
        notification={notification}
        onClose={handleNotificationClose}
      />

      {createdUser ? (
        <CreatedUserSuccess
          createdUser={createdUser}
          onPrintCredentials={handlePrintCredentials}
          onCreateAnother={handleReset}
        />
      ) : (
        <form onSubmit={handleSubmit}>
          <div className="p-6 rounded-xl border border-gray-100 shadow-md bg-white">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Create New User
            </h2>

            {/* Step Progress Indicator */}
            <StepProgressIndicator currentStep={currentStep} stepTitles={stepTitles} />

            <div className="grid grid-cols-1 gap-6">
              {/* Step 1: School & Branch Selection */}
              {currentStep === 1 && (
                <SchoolBranchSelector
                  schools={schools}
                  branches={branches}
                  selectedSchool={formData.school}
                  selectedBranch={formData.school_branch}
                  onSchoolChange={handleSchoolChange}
                  onBranchChange={handleBranchChange}
                  onBlur={handleBlur}
                  isLoadingSchools={isLoadingSchools}
                  isLoadingBranches={isLoadingBranches}
                  schoolError={errors.school}
                  branchError={errors.school_branch}
                />
              )}

              {/* Step 2: Basic Information */}
              {currentStep === 2 && (
                <BasicInformationForm
                  formData={formData}
                  errors={errors}
                  onChange={handleChange}
                  onBlur={handleBlur}
                />
              )}

              {/* Step 3: User Type Selection */}
              {currentStep === 3 && (
                <div className="bg-green-50 p-4 rounded-lg border border-green-100 mb-4">
                  <h3 className="text-md font-medium text-green-800 mb-3">User Type & Role</h3>
                  <UserTypeSelector
                    userTypeCategories={userTypeCategories}
                    selectedUserType={formData.user_type}
                    onChange={handleUserTypeChange}
                    error={errors.user_type}
                  />
                </div>
              )}

              {/* Step 4: Role-Specific Information */}
              {currentStep === 4 && formData.user_type === 'student' && (
                <StudentForm
                  formData={formData}
                  errors={errors}
                  classes={classes}
                  streams={streams}
                  isLoadingClasses={isLoadingClasses}
                  isLoadingStreams={isLoadingStreams}
                  onChange={handleChange}
                  onClassChange={handleClassChange}
                  onFileSelect={handleFileSelect}
                  onRemoveFile={(fieldName) => () => handleRemoveFile(fieldName)}
                />
              )}

              {currentStep === 4 && formData.user_type === 'teacher' && (
                <TeacherForm
                  formData={formData}
                  errors={errors}
                  departments={departments}
                  subjects={subjects}
                  classes={classes}
                  isLoadingDepartments={isLoadingDepartments}
                  isLoadingSubjects={isLoadingSubjects}
                  isLoadingClasses={isLoadingClasses}
                  onChange={handleChange}
                  onCheckboxChange={handleCheckboxChange}
                  onMultiSelectChange={handleMultiSelectChange}
                  onFileSelect={handleFileSelect}
                  onRemoveFile={(fieldName) => () => handleRemoveFile(fieldName)}
                />
              )}

              {currentStep === 4 && formData.user_type === 'staff' && (
                <StaffForm
                  formData={formData}
                  errors={errors}
                  roles={roles}
                  isLoadingRoles={isLoadingRoles}
                  onChange={handleChange}
                />
              )}

              {currentStep === 4 && formData.user_type === 'parent' && (
                <ParentForm
                  formData={formData}
                  errors={errors}
                  students={students}
                  isLoadingStudents={isLoadingStudents}
                  onChange={handleChange}
                  onChildrenChange={handleChildrenChange}
                />
              )}

              {/* Profile Information (shown on all steps except step 5) */}
              {currentStep !== 5 && (
                <ProfileInformationForm
                  formData={formData}
                  onChange={handleChange}
                  onFileSelect={handleFileSelect('profile_picture')}
                  onRemoveFile={handleRemoveFile}
                />
              )}

              {/* Password Information */}
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                <div className="text-blue-800 font-medium flex items-center mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Password Information
                </div>
                <p className="text-sm text-blue-700">
                  A secure temporary password will be automatically generated for this user. They will be prompted to change it on first login.
                </p>
              </div>
            </div>

            <hr className="my-6 border-gray-200" />

            <div className="flex justify-between mt-6">
              {/* Step Navigation Buttons */}
              <div className="flex gap-3">
                {currentStep > 1 && (
                  <button
                    type="button"
                    onClick={goToPreviousStep}
                    className="flex items-center justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                    Previous
                  </button>
                )}

                {currentStep < totalSteps && (
                  <button
                    type="button"
                    onClick={handleNextStep}
                    className="flex items-center justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Next
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3">
                <button
                  type="button"
                  onClick={handleReset}
                  disabled={isSubmitting}
                  className="flex items-center justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Reset Form
                </button>

                {currentStep === totalSteps && (
                  <button
                    type="button"
                    onClick={() => setShowConfirmation(true)}
                    disabled={isSubmitting}
                    className="flex items-center justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Creating...
                      </>
                    ) : (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                        </svg>
                        Review & Create User
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </form>
      )}

      {/* Notification snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleNotificationClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleNotificationClose} severity={notification.type as AlertColor}>
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Confirmation Dialog */}
      {showConfirmation && (
        <ConfirmationDialog
          formData={formData}
          isSubmitting={isSubmitting}
          onConfirm={handleConfirmationConfirm}
          onCancel={handleConfirmationClose}
          schools={schools}
          branches={branches}
          departments={departments}
          students={students}
        />
      )}
    </>
  );
};

export default CreateUserForm;
