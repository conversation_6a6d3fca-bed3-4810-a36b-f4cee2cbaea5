import React, { useState, useEffect } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import FormInput from '../components/FormInput';
import Table from '../components/Table';
import Pagination from '../components/Pagination';
import { useNotification } from '../components/NotificationProvider';
import userService, { User, UserType, CreateUserRequest, UpdateUserRequest } from '../services/userService';

const UserManagementPage: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isUserModalOpen, setIsUserModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [filterUserType, setFilterUserType] = useState<UserType | ''>('');
  const [searchTerm, setSearchTerm] = useState('');
  const { showSuccess, showError } = useNotification();

  const { 
    register, 
    handleSubmit, 
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm<CreateUserRequest | UpdateUserRequest>();

  useEffect(() => {
    fetchUsers();
  }, [filterUserType]);

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      const response = await userService.getUsers(
        filterUserType ? filterUserType as UserType : undefined,
        undefined,
        searchTerm || undefined
      );
      setUsers(response.data);
    } catch (error) {
      console.error('Error fetching users:', error);
      showError('Failed to fetch users');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = () => {
    fetchUsers();
  };

  const handleUserSelect = (user: User) => {
    setSelectedUser(user);
  };

  const openUserModal = (user?: User) => {
    if (user) {
      setSelectedUser(user);
      reset({
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        userType: user.userType,
        isActive: user.isActive
      });
    } else {
      setSelectedUser(null);
      reset({
        username: '',
        email: '',
        firstName: '',
        lastName: '',
        userType: UserType.TEACHER,
        password: '',
        roleId: 0
      });
    }
    setIsUserModalOpen(true);
  };

  const onSubmitUser: SubmitHandler<CreateUserRequest | UpdateUserRequest> = async (data) => {
    try {
      setIsLoading(true);
      
      if (selectedUser) {
        await userService.updateUser(selectedUser.id, data as UpdateUserRequest);
        showSuccess('User updated successfully');
      } else {
        await userService.createUser(data as CreateUserRequest);
        showSuccess('User created successfully');
      }
      
      fetchUsers();
      setIsUserModalOpen(false);
    } catch (error) {
      console.error('Error saving user:', error);
      showError('Failed to save user');
    } finally {
      setIsLoading(false);
    }
  };

  // Pagination
  const indexOfLastUser = currentPage * itemsPerPage;
  const indexOfFirstUser = indexOfLastUser - itemsPerPage;
  const currentUsers = users.slice(indexOfFirstUser, indexOfLastUser);

  const userColumns = [
    { 
      header: 'Name', 
      accessor: 'firstName',
      cell: (_: string, row: User) => (
        <div>
          <div className="font-medium text-gray-900">{`${row.firstName} ${row.lastName}`}</div>
          <div className="text-sm text-gray-500">{row.email}</div>
        </div>
      )
    },
    { 
      header: 'Username', 
      accessor: 'username' 
    },
    { 
      header: 'User Type', 
      accessor: 'userType',
      cell: (value: UserType) => (
        <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
          {value.replace(/_/g, ' ')}
        </span>
      )
    },
    { 
      header: 'Status', 
      accessor: 'isActive',
      cell: (value: boolean) => (
        <span className={`px-2 py-1 text-xs rounded-full ${
          value ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
        }`}>
          {value ? 'Active' : 'Inactive'}
        </span>
      )
    },
    { 
      header: 'Last Login', 
      accessor: 'lastLogin',
      cell: (value: string) => value ? new Date(value).toLocaleString() : 'Never'
    },
    { 
      header: 'Actions', 
      accessor: 'id',
      cell: (_: any, row: User) => (
        <div className="flex space-x-2">
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              openUserModal(row);
            }}
            className="text-blue-600 hover:text-blue-800"
          >
            Edit
          </button>
        </div>
      )
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
        <button
          type="button"
          onClick={() => openUserModal()}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Add New User
        </button>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex flex-col md:flex-row md:items-end space-y-4 md:space-y-0 md:space-x-4">
          <div className="flex-1">
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
              Search Users
            </label>
            <div className="flex">
              <input
                id="search"
                type="text"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm flex-1"
                placeholder="Search by name, email or username"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <button
                type="button"
                onClick={handleSearch}
                className="ml-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Search
              </button>
            </div>
          </div>

          <div>
            <label htmlFor="filterUserType" className="block text-sm font-medium text-gray-700 mb-1">
              Filter by User Type
            </label>
            <select
              id="filterUserType"
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              value={filterUserType}
              onChange={(e) => setFilterUserType(e.target.value as UserType | '')}
            >
              <option value="">All User Types</option>
              {Object.values(UserType).map((type) => (
                <option key={type} value={type}>
                  {type.replace(/_/g, ' ')}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <div className="space-y-4">
          <Table
            columns={userColumns}
            data={currentUsers}
            isLoading={isLoading}
            emptyMessage="No users found"
            onRowClick={handleUserSelect}
          />

          <Pagination
            totalItems={users.length}
            itemsPerPage={itemsPerPage}
            currentPage={currentPage}
            onPageChange={setCurrentPage}
          />
        </div>
      </div>

      {/* User Modal */}
      {isUserModalOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <form onSubmit={handleSubmit(onSubmitUser)}>
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    {selectedUser ? 'Edit User' : 'Add User'}
                  </h3>
                  <div className="mt-4 space-y-4">
                    {!selectedUser && (
                      <FormInput
                        id="username"
                        label="Username"
                        register={register}
                        registerOptions={{ required: 'Username is required' }}
                        error={errors.username?.message}
                        required
                      />
                    )}

                    <FormInput
                      id="email"
                      label="Email"
                      type="email"
                      register={register}
                      registerOptions={{ 
                        required: 'Email is required',
                        pattern: {
                          value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                          message: 'Invalid email address'
                        }
                      }}
                      error={errors.email?.message}
                      required
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormInput
                        id="firstName"
                        label="First Name"
                        register={register}
                        registerOptions={{ required: 'First name is required' }}
                        error={errors.firstName?.message}
                        required
                      />

                      <FormInput
                        id="lastName"
                        label="Last Name"
                        register={register}
                        registerOptions={{ required: 'Last name is required' }}
                        error={errors.lastName?.message}
                        required
                      />
                    </div>

                    {!selectedUser && (
                      <FormInput
                        id="password"
                        label="Password"
                        type="password"
                        register={register}
                        registerOptions={{ 
                          required: 'Password is required',
                          minLength: {
                            value: 8,
                            message: 'Password must be at least 8 characters'
                          }
                        }}
                        error={errors.password?.message}
                        required
                      />
                    )}

                    <div>
                      <label htmlFor="userType" className="block text-sm font-medium text-gray-700">
                        User Type
                      </label>
                      <select
                        id="userType"
                        className={`mt-1 form-input ${errors.userType ? 'border-red-500' : ''}`}
                        {...register('userType', { required: 'User type is required' })}
                      >
                        {Object.values(UserType).map((type) => (
                          <option key={type} value={type}>
                            {type.replace(/_/g, ' ')}
                          </option>
                        ))}
                      </select>
                      {errors.userType && (
                        <p className="mt-1 text-sm text-red-600">{errors.userType.message}</p>
                      )}
                    </div>

                    {!selectedUser && (
                      <FormInput
                        id="roleId"
                        label="Role ID"
                        type="number"
                        register={register}
                        registerOptions={{ required: 'Role ID is required' }}
                        error={errors.roleId?.message}
                        required
                      />
                    )}

                    {selectedUser && (
                      <div className="flex items-center">
                        <input
                          id="isActive"
                          type="checkbox"
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          {...register('isActive')}
                        />
                        <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                          Active
                        </label>
                      </div>
                    )}
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    ) : null}
                    {selectedUser ? 'Update' : 'Create'}
                  </button>
                  <button
                    type="button"
                    onClick={() => setIsUserModalOpen(false)}
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagementPage;
