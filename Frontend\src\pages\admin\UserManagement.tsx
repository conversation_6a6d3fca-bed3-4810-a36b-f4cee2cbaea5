import { useState, useEffect } from 'react';
import {
  ArrowPathIcon,
  UsersIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import PageBreadcrumb from '../../components/common/PageBreadCrumb';
import PageMeta from '../../components/common/PageMeta';
import UsersList from '../../components/users/UsersList';
import { useSchool } from '../../contexts/SchoolContext';
import SchoolBranchFilter from '../../components/ui/filters/SchoolBranchFilter';

const UserManagement = () => {
  // State to trigger refresh
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Get school context
  const { selectedSchool, selectedBranch } = useSchool();

  return (
    <>
      <PageMeta
        title="User Management | ShuleXcel"
        description="Manage users and invitations in your ShuleXcel system."
      />

      <PageBreadcrumb pageTitle="User Management" />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4">
        {/* Dashboard Header */}
        <div className="mb-6 bg-white rounded-xl overflow-hidden shadow-md border border-gray-200">
          <div className="px-6 py-5">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div className="mb-4 md:mb-0">
                <div className="flex items-center mb-2">
                  <UsersIcon className="h-6 w-6 text-indigo-600 mr-2" />
                  <h1 className="text-2xl font-bold text-gray-800">
                    User Management
                  </h1>
                </div>
                <p className="text-sm text-gray-600">
                  Manage user accounts, permissions, and profiles
                </p>
              </div>
              <div>
                <SchoolBranchFilter
                  showAllOption={false}
                  className="min-w-[300px]"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Context Warning (only show if no school selected) */}
        {!selectedSchool || !selectedBranch ? (
          <div className="mb-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start">
              <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-medium text-yellow-800">
                  No School Selected
                </h3>
                <p className="text-sm text-yellow-700 mt-1">
                  Please select a school and branch from the school switcher to manage users.
                </p>
              </div>
            </div>
          </div>
        ) : null}

        {/* Users List */}
        <div className="mb-6 overflow-hidden shadow-md rounded-xl border border-gray-100 bg-white">
          <div className="p-4">
            <div className="mb-4 flex justify-between items-center">
              <h2 className="text-lg font-semibold text-gray-900">
                Manage Users
              </h2>
              <button
                type="button"
                onClick={() => setRefreshTrigger(prev => prev + 1)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <ArrowPathIcon className="h-5 w-5 mr-2" />
                Refresh
              </button>
            </div>
            <UsersList
              refreshTrigger={refreshTrigger}
              schoolId={selectedSchool?.id}
              branchId={selectedBranch?.id}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default UserManagement;
