import axios, { AxiosError } from 'axios';
import { API_BASE_URL } from '../config/constants.ts';
import { LoginRequest, LoginResponse, User, UserProfile } from '../types/auth';
import { extractOriginalToken } from '../utils/tokenUtils';

// Define types for registration
interface RegisterRequest {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  [key: string]: string; // For additional fields
}

interface RegisterResponse {
  success: boolean;
  message: string;
  user?: User;
  [key: string]: unknown; // For additional fields
}

// API endpoints for authentication
const API_URL = `${API_BASE_URL}/api/core/auth/`;
const TOKEN_REFRESH_URL = `${API_BASE_URL}/api/core/auth/token/refresh/`;
const TOKEN_VERIFY_URL = `${API_BASE_URL}/api/core/auth/token/verify/`;
const USER_PROFILE_URL = `${API_BASE_URL}/api/users/profile/`;

// Create axios instance
const axiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include the token in requests
axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export const authService = {
  /**
   * Register a new user (single step - for backward compatibility)
   *
   * @param userData - User registration data
   * @returns Promise with registration response
   */
  register: async (userData: RegisterRequest): Promise<RegisterResponse> => {
    try {
      const response = await axiosInstance.post('register/', userData);

      // Store the token in localStorage
      if (response.data.access) {
        localStorage.setItem('token', response.data.access);
        localStorage.setItem('refreshToken', response.data.refresh);
      }

      return response.data;
    } catch (error: unknown) {
      // Type guard for axios error
      if (error instanceof AxiosError && error.response) {
        // Handle specific error cases if needed
      }

      throw new Error('Registration failed. Please try again.');
    }
  },

  /**
   * Register a new user - Step One
   *
   * @param userData - Basic user registration data
   * @returns Promise with registration response
   */
 

  /**
   * Login a user
   *
   * @param credentials - User login credentials
   * @returns Promise with login response
   */
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    try {
      // Use direct axios call to avoid interceptors during login
      const response = await axios.post(`${API_URL}login/`, credentials, {
        headers: {
          'Content-Type': 'application/json',
        }
      });

      // Store the token in localStorage
      if (response.data.access) {
        localStorage.setItem('token', response.data.access);
        localStorage.setItem('refreshToken', response.data.refresh);

        // Also store user data
        if (response.data.user) {
          localStorage.setItem('user', JSON.stringify(response.data.user));
        }
      }

      return response.data;
    } catch {
      throw new Error('Login failed. Please check your credentials and try again.');
    }
  },

  /**
   * Logout a user
   *
   * @returns Promise with logout response
   */
  logout: async (): Promise<{ success: boolean; message: string }> => {
    try {
      // Simply clear tokens from localStorage without making an API call
      // This is a simpler approach that works reliably
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');

      return { success: true, message: 'Logged out successfully' };
    } catch {
      // Even if there's an error, try to clear the tokens
      try {
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('user');
      } catch {
        // Handle storage error if needed
      }

      // Return success anyway since we've tried to clear the tokens
      return { success: true, message: 'Logged out successfully (with warnings)' };
    }
  },

  /**
   * Check if a user is authenticated
   *
   * @returns True if authenticated, false otherwise
   */
  isAuthenticated: (): boolean => {
    return !!localStorage.getItem('token');
  },

  /**
   * Get the current user's token
   *
   * @returns The token or null if not authenticated
   */
  getToken: (): string | null => {
    return localStorage.getItem('token');
  },

  /**
   * Get the current user's refresh token
   *
   * @returns The refresh token or null if not authenticated
   */
  getRefreshToken: (): string | null => {
    return localStorage.getItem('refreshToken');
  },

  /**
   * Refresh the access token using the refresh token
   *
   * @returns Promise with refresh response
   */
  refreshToken: async (): Promise<{ access: string }> => {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) {
        throw new Error('No refresh token found');
      }

      // Use direct axios call to avoid interceptors during token refresh
      const response = await axios.post(TOKEN_REFRESH_URL, {
        refresh: refreshToken
      }, {
        headers: {
          'Content-Type': 'application/json',
        }
      });

      // Store the new access token in localStorage
      if (response.data.access) {
        localStorage.setItem('token', response.data.access);
      } else {
        throw new Error('Invalid refresh response');
      }

      return response.data;
    } catch (error) {
      localStorage.removeItem('user');

      // Provide more specific error message
      if (axios.isAxiosError(error) && error.response) {
        if (error.response.status === 401) {
          throw new Error('Session expired. Please log in again.');
        } else {
          throw new Error(`Token refresh failed: ${error.response.status} ${error.response.statusText}`);
        }
      }

      throw error;
    }
  },

  /**
   * Get the current user's profile information
   *
   * @returns Promise with user profile data
   */
  getCurrentUser: async (): Promise<User> => {
    return JSON.parse(localStorage.getItem('user') || '{}');
  },

  /**
   * Refresh user data in the background
   *
   * @returns Promise with user profile data
   */
  refreshUserData: async (): Promise<User> => {
    try {
      const response = await axiosInstance.get(USER_PROFILE_URL);
      const user: User = response.data;

      // Update localStorage with the refreshed user data
      localStorage.setItem('user', JSON.stringify(user));

      return user;
    } catch (error) {
      throw new Error('Failed to refresh user data. Please try again later.');
    }
  },

  /**
   * Update the user's profile
   *
   * @param userData - User profile data to update
   * @returns Promise with updated profile data
   */
  updateProfile: async (userData: UserProfile | FormData): Promise<User> => {
    try {
      // Create a new instance for FormData support
      const formDataInstance = axios.create({
        baseURL: import.meta.env.VITE_API_URL || 'http://127.0.0.1:8000/api/',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          // Don't set Content-Type here, it will be set automatically for FormData
        },
      });

      // Use the correct API endpoint
      const response = await formDataInstance.put('/users/me/', userData);

      // No mock data - we want to work with real data only
      if (!response.data) {
        throw new Error('No data returned from profile update API');
      }
      return response.data;
    } catch (error: unknown) {
      // Handle specific error cases if needed
      if (error instanceof Error) {
        // Handle error message
      }
      if (axios.isAxiosError(error) && error.response) {
        // Handle axios error response
      }

      // No mock data - we want to work with real data only
      // We'll throw the error to be handled by the calling component
      throw error;
    }
  },

  /**
   * Check if the token is valid
   *
   * @returns {Promise<boolean>} True if the token is valid, false otherwise
   */
  isTokenValid: async (): Promise<boolean> => {
      const token = localStorage.getItem('token');
      if (!token) {
        return false;
      }

      try {
        await axios.post(TOKEN_VERIFY_URL, { token }, {
          baseURL: API_BASE_URL,
          headers: {
            'Content-Type': 'application/json',
          },
        });
        return true;
      } catch {
        return false;
      }
    },

  /**
   * Request a password reset email
   *
   * @param email - The email address to send the reset link to
   * @returns Promise with the response
   */
  requestPasswordReset: async (email: string): Promise<{ status: string; message: string }> => {
    try {
      // Use the consolidated password reset endpoint
      const response = await axios.post(`${API_BASE_URL}/api/core/password-reset-direct/`, {
        email
      });
      console.log('Password reset request response:', response.data);
      return {
        status: 'OK',
        message: response.data.detail || 'Password reset email has been sent.'
      };
    } catch (error: any) {
      console.error('Password reset request error:', error);

      // Pass through the actual error response for better error handling
      if (error.response) {
        throw error;
      }

      throw new Error('Failed to request password reset. Please try again later.');
    }
  },

  /**
   * Validate a password reset token
   *
   * @param token - The reset token from the email
   * @returns Promise with the validation result
   */
  validateResetToken: async (token: string): Promise<{ valid: boolean; message: string }> => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/core/password-reset/validate-token/`, {
        token
      });
      return response.data;
    } catch (error: any) {
      console.error('Token validation error:', error);

      // Return invalid token with error message
      if (error.response && error.response.data) {
        return {
          valid: false,
          message: error.response.data.message || 'Invalid or expired token'
        };
      }

      return { valid: false, message: 'Failed to validate token' };
    }
  },

  /**
   * Reset password with token
   *
   * @param token - The reset token from the email (can be secure token or original token)
   * @param password - The new password
   * @param confirmPassword - Confirmation of the new password
   * @returns Promise with the response
   */
  resetPassword: async (token: string, password: string, confirmPassword: string): Promise<{ status: string; message: string }> => {
    try {
      console.log('Resetting password with token:', token);

      // Use our consolidated endpoint for all token types
      const response = await axios.post(`${API_BASE_URL}/api/core/password-reset/reset-with-token/`, {
        token,
        password,
        password_confirm: confirmPassword
      });

      console.log('Password reset response:', response.data);
      return {
        status: response.data.status || 'OK',
        message: response.data.message || 'Password has been reset successfully'
      };
    } catch (error: any) {
      console.error('Password reset error details:', error);

      if (error.response) {
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);

        // Pass through the actual error response for better error handling
        throw error;
      }

      throw new Error('Failed to reset password. The token may be invalid or expired.');
    }
  },

  /**
   * Change user password
   *
   * @param currentPassword - The current password
   * @param newPassword - The new password
   * @param confirmPassword - Confirmation of the new password
   * @returns Promise with the response
   */
  changePassword: async (currentPassword: string, newPassword: string, confirmPassword: string): Promise<{ status: string; message: string }> => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('No access token found');
      }

      const response = await axios.post(
        `${API_BASE_URL}/api/users/change-password/`,
        {
          current_password: currentPassword,
          new_password: newPassword,
          confirm_password: confirmPassword
        },
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.detail) {
        throw new Error(error.response.data.detail);
      }
      throw new Error('Failed to change password. Please try again.');
    }
  }
};

export default authService;
