/**
 * Utility functions for handling routes and navigation
 */

/**
 * Get the home path based on user role
 * @param user The user object
 * @returns The path to redirect to based on user role
 */
export const getRoleBasedHomePath = (user: any): string => {
  if (!user) return '/signin';

  // Check if user is a superuser (highest priority)
  if (user.is_superuser) {
    return '/admin/dashboard';
  }

  // Check user type
  const userType = user.user_type?.toLowerCase();

  switch (userType) {
    case 'admin':
      return '/admin/dashboard';
    case 'system_admin':
      return '/admin/dashboard';
    case 'teacher':
      return '/teacher/dashboard';
    case 'student':
      return '/student/dashboard';
    case 'parent':
      return '/parent/dashboard';
    default:
      // Default to the main dashboard if role is unknown
      return '/';
  }
};

/**
 * Get the profile page path based on user role
 * @param user The user object
 * @returns The path to the user's profile page
 */
export const getRoleBasedProfilePath = (user: any): string => {
  if (!user) return '/profile';

  // Check if user is a superuser (highest priority)
  if (user.is_superuser) {
    return '/admin/profile';
  }

  // Check user type
  const userType = user.user_type?.toLowerCase();

  switch (userType) {
    case 'admin':
    case 'system_admin':
    case 'school_admin':
    case 'deputy_principal':
    case 'branch_admin':
    case 'department_head':
    case 'ict_admin':
      return '/admin/profile';
    case 'teacher':
      return '/teacher/profile';
    case 'student':
      return '/student/profile';
    case 'parent':
      return '/parent/profile';
    case 'staff':
    case 'librarian':
    case 'counselor':
    case 'accountant':
    case 'secretary':
    case 'nurse':
    case 'maintenance':
    case 'security':
    case 'driver':
      return '/staff/profile';
    default:
      // Default to general profile if role is unknown
      return '/profile';
  }
};

/**
 * Get the dashboard path based on user role
 * @param user The user object
 * @returns The path to the user's dashboard
 */
export const getRoleBasedDashboardPath = (user: any): string => {
  if (!user) return '/dashboard';

  // Check if user is a superuser (highest priority)
  if (user.is_superuser) {
    return '/admin/dashboard';
  }

  // Check user type
  const userType = user.user_type?.toLowerCase();

  switch (userType) {
    case 'admin':
    case 'system_admin':
    case 'school_admin':
    case 'deputy_principal':
    case 'branch_admin':
    case 'department_head':
    case 'ict_admin':
      return '/admin/dashboard';
    case 'teacher':
      return '/teacher/dashboard';
    case 'student':
      return '/student/dashboard';
    case 'parent':
      return '/parent/dashboard';
    case 'staff':
    case 'librarian':
    case 'counselor':
    case 'accountant':
    case 'secretary':
    case 'nurse':
    case 'maintenance':
    case 'security':
    case 'driver':
      return '/staff/dashboard';
    default:
      // Default to general dashboard if role is unknown
      return '/dashboard';
  }
};

/**
 * Get the sidebar items based on user role
 * @param user The user object
 * @returns Array of sidebar items the user should see
 */
export const getRoleBasedSidebarItems = (user: any): string[] => {
  if (!user) return [];

  const commonItems = ['profile', 'calendar', 'messages'];

  // Superuser can see everything
  if (user.is_superuser) {
    return ['all']; // Special value to indicate all items should be shown
  }

  // Role-specific items
  const roleItems: Record<string, string[]> = {
    admin: [
      ...commonItems,
      'admin',
      'schools',
      'users',
      'settings',
      'academics',
      'library',
      'inventory',
      'communication'
    ],
    system_admin: [
      ...commonItems,
      'admin',
      'schools',
      'users',
      'settings',
      'academics',
      'library',
      'inventory',
      'communication'
    ],
    teacher: [
      ...commonItems,
      'academics',
      'students',
      'classes',
      'assessments',
      'grades',
      'attendance',
      'communication'
    ],
    student: [
      ...commonItems,
      'academics',
      'assignments',
      'grades',
      'attendance',
      'timetable',
      'exams'
    ],
    parent: [
      ...commonItems,
      'children',
      'academics',
      'payments',
      'attendance',
      'grades',
      'communication'
    ]
  };

  const userType = user.user_type?.toLowerCase();
  return roleItems[userType] || commonItems;
};

/**
 * Check if a user has access to a specific route
 * @param user The user object
 * @param route The route to check
 * @returns Boolean indicating if the user has access
 */
export const hasRouteAccess = (user: any, route: string): boolean => {
  if (!user) return false;

  // Superuser has access to everything
  if (user.is_superuser) return true;

  const allowedRoutes = getRoleBasedSidebarItems(user);

  // Special case for 'all'
  if (allowedRoutes.includes('all')) return true;

  // Check if any of the allowed routes is a prefix of the requested route
  return allowedRoutes.some(item => route.startsWith(`/${item}`));
};

export default {
  getRoleBasedHomePath,
  getRoleBasedSidebarItems,
  hasRouteAccess
};
